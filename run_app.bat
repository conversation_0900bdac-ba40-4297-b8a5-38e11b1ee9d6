@echo off
chcp 65001 >nul
color 0B

echo.
echo   ██████╗ ███████╗██╗   ██╗███████╗██████╗ ██╗███████╗
echo   ██╔══██╗██╔════╝██║   ██║██╔════╝██╔══██╗██║██╔════╝
echo   ██████╔╝█████╗  ██║   ██║█████╗  ██████╔╝██║█████╗  
echo   ██╔══██╗██╔══╝  ╚██╗ ██╔╝██╔══╝  ██╔══██╗██║██╔══╝  
echo   ██║  ██║███████╗ ╚████╔╝ ███████╗██║  ██║██║███████╗
echo   ╚═╝  ╚═╝╚══════╝  ╚═══╝  ╚══════╝╚═╝  ╚═╝╚═╝╚══════╝
echo.

:: 检查虚拟环境
if not exist "venv\Scripts\activate.bat" (
    echo   ❌ [错误] 虚拟环境不存在，请先运行 setup.bat
    pause
    exit /b 1
)

:: 激活虚拟环境
echo   🔧 [信息] 激活虚拟环境...
call venv\Scripts\activate.bat

:: 启动应用
echo   🚀 [信息] 启动Reverie Agents桌面应用...
echo.
python ui\app\main.py

echo.
echo   ✨ 感谢使用 Reverie Agents！
pause