"""
聊天组件
实现ChatGPT风格的对话界面
"""

import os
from typing import List, Dict, Any
from datetime import datetime

from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QTextEdit,
                            QScrollArea, QFrame, QPushButton, QLabel, QSizePolicy)
from PyQt6.QtCore import Qt, pyqtSignal, QTimer, QPropertyAnimation, QEasingCurve
from PyQt6.QtGui import QFont, QPixmap, QTextCursor, QKeySequence

from core.utils.logger import get_logger
from ui.app.components.loading_widget import TypingIndicator

logger = get_logger(__name__)

class MessageBubble(QFrame):
    """消息气泡"""
    
    def __init__(self, message: str, is_user: bool = True, avatar_path: str = None, persona_name: str = "AI助手"):
        super().__init__()
        self.message = message
        self.is_user = is_user
        self.avatar_path = avatar_path
        self.persona_name = persona_name

        self.init_ui()
    
    def init_ui(self):
        """初始化UI"""
        # 设置对象名称用于样式
        if self.is_user:
            self.setObjectName("user_message")
        else:
            self.setObjectName("assistant_message")

        # 主布局
        main_layout = QHBoxLayout(self)
        main_layout.setContentsMargins(20, 15, 20, 15)
        main_layout.setSpacing(15)

        if not self.is_user:
            # AI消息布局 - 模仿ChatGPT左对齐设计
            # AI头像 - 固定在左侧
            avatar_container = QWidget()
            avatar_container.setFixedSize(32, 32)
            avatar_container.setStyleSheet("""
                QWidget {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 #10a37f, stop:1 #1a7f64);
                    border-radius: 16px;
                    border: 1px solid rgba(255,255,255,0.1);
                }
            """)

            avatar_layout = QVBoxLayout(avatar_container)
            avatar_layout.setContentsMargins(0, 0, 0, 0)

            if self.avatar_path and os.path.exists(self.avatar_path):
                avatar_label = QLabel()
                pixmap = QPixmap(self.avatar_path).scaled(28, 28, Qt.AspectRatioMode.KeepAspectRatio,
                                                        Qt.TransformationMode.SmoothTransformation)
                avatar_label.setPixmap(pixmap)
                avatar_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
                avatar_layout.addWidget(avatar_label)
            else:
                avatar_label = QLabel("🤖")
                avatar_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
                avatar_label.setStyleSheet("font-size: 16px; background: transparent; border: none; color: white;")
                avatar_layout.addWidget(avatar_label)

            main_layout.addWidget(avatar_container, 0, Qt.AlignmentFlag.AlignTop)

            # AI角色名称标签
            ai_role_header = QWidget()
            ai_role_layout = QHBoxLayout(ai_role_header)
            ai_role_layout.setContentsMargins(0, 0, 0, 5)
            ai_role_layout.setSpacing(8)

            ai_role_name = QLabel(self.persona_name)
            ai_role_name.setStyleSheet("""
                QLabel {
                    color: #10b981;
                    font-size: 12px;
                    font-weight: 600;
                    background: transparent;
                    border: none;
                }
            """)
            ai_role_layout.addWidget(ai_role_name)
            ai_role_layout.addStretch()

            main_layout.addWidget(ai_role_header, 0, Qt.AlignmentFlag.AlignTop)

            # 消息内容区域 - 使用现代化气泡设计
            message_container = QWidget()
            message_container.setMaximumWidth(650)
            message_container.setStyleSheet("""
                QWidget {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 rgba(16, 185, 129, 0.08), stop:1 rgba(5, 150, 105, 0.12));
                    border: 1px solid rgba(16, 185, 129, 0.2);
                    border-radius: 20px;
                    padding: 0;
                }
            """)

            message_layout = QVBoxLayout(message_container)
            message_layout.setContentsMargins(16, 12, 16, 12)

            # 消息内容
            message_label = QLabel(self.message)
            message_label.setObjectName("ai_message")
            message_label.setWordWrap(True)
            message_label.setTextInteractionFlags(Qt.TextInteractionFlag.TextSelectableByMouse)
            message_label.setStyleSheet("""
                QLabel {
                    background: transparent;
                    color: #e2e8f0;
                    font-size: 14px;
                    line-height: 1.6;
                    border: none;
                }
            """)
            message_layout.addWidget(message_label)

            main_layout.addWidget(message_container, 1)
            main_layout.addStretch(2)  # 右侧留白

        else:
            # 用户消息布局 - 模仿ChatGPT右对齐设计
            main_layout.addStretch(2)  # 左侧留白

            # 用户角色名称标签
            user_role_header = QWidget()
            user_role_layout = QHBoxLayout(user_role_header)
            user_role_layout.setContentsMargins(0, 0, 0, 5)
            user_role_layout.setSpacing(8)

            user_role_layout.addStretch()
            user_role_name = QLabel("您")
            user_role_name.setStyleSheet("""
                QLabel {
                    color: #3b82f6;
                    font-size: 12px;
                    font-weight: 600;
                    background: transparent;
                    border: none;
                }
            """)
            user_role_layout.addWidget(user_role_name)

            main_layout.addWidget(user_role_header, 0, Qt.AlignmentFlag.AlignTop)

            # 消息内容区域 - 使用现代化气泡设计
            message_container = QWidget()
            message_container.setMaximumWidth(650)
            message_container.setStyleSheet("""
                QWidget {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 rgba(59, 130, 246, 0.12), stop:1 rgba(29, 78, 216, 0.08));
                    border: 1px solid rgba(59, 130, 246, 0.3);
                    border-radius: 20px;
                    padding: 0;
                }
            """)

            message_layout = QVBoxLayout(message_container)
            message_layout.setContentsMargins(16, 12, 16, 12)

            # 消息内容
            message_label = QLabel(self.message)
            message_label.setWordWrap(True)
            message_label.setTextInteractionFlags(Qt.TextInteractionFlag.TextSelectableByMouse)
            message_label.setStyleSheet("""
                QLabel {
                    background: transparent;
                    color: #e2e8f0;
                    font-size: 14px;
                    line-height: 1.6;
                    border: none;
                }
            """)
            message_layout.addWidget(message_label)

            main_layout.addWidget(message_container, 1)

            # 用户头像 - 固定在右侧
            user_avatar_container = QWidget()
            user_avatar_container.setFixedSize(32, 32)
            user_avatar_container.setStyleSheet("""
                QWidget {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 #ab68ff, stop:1 #6366f1);
                    border-radius: 16px;
                    border: 1px solid rgba(255,255,255,0.1);
                }
            """)

            user_avatar_layout = QVBoxLayout(user_avatar_container)
            user_avatar_layout.setContentsMargins(0, 0, 0, 0)

            user_avatar_label = QLabel("👤")
            user_avatar_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            user_avatar_label.setStyleSheet("font-size: 16px; background: transparent; border: none; color: white;")
            user_avatar_layout.addWidget(user_avatar_label)

            main_layout.addWidget(user_avatar_container, 0, Qt.AlignmentFlag.AlignTop)

class TypingIndicator(QFrame):
    """输入指示器"""
    
    def __init__(self):
        super().__init__()
        self.dots = ["⚫", "⚪", "⚪"]
        self.current_dot = 0
        self.setObjectName("typing_indicator_frame")
        
        self.init_ui()
        self.setup_animation()
    
    def init_ui(self):
        """初始化UI"""
        layout = QHBoxLayout(self)
        layout.setContentsMargins(20, 15, 20, 15)
        layout.setSpacing(15)
        
        # AI头像
        avatar_container = QWidget()
        avatar_container.setFixedSize(32, 32)
        avatar_container.setStyleSheet("""
            QWidget {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #10a37f, stop:1 #1a7f64);
                border-radius: 16px;
                border: 1px solid rgba(255,255,255,0.1);
            }
        """)
        
        avatar_layout = QVBoxLayout(avatar_container)
        avatar_layout.setContentsMargins(0, 0, 0, 0)
        
        avatar_label = QLabel("🤖")
        avatar_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        avatar_label.setStyleSheet("font-size: 16px; background: transparent; border: none; color: white;")
        avatar_layout.addWidget(avatar_label)
        
        layout.addWidget(avatar_container, 0, Qt.AlignmentFlag.AlignTop)
        
        # 输入指示器容器
        indicator_container = QWidget()
        indicator_container.setStyleSheet("""
            QWidget {
                background: rgba(52, 53, 65, 0.7);
                border-radius: 12px;
                padding: 5px;
            }
        """)
        
        indicator_layout = QVBoxLayout(indicator_container)
        indicator_layout.setContentsMargins(10, 5, 10, 5)
        
        # 输入指示器
        self.indicator_label = QLabel("正在思考")
        self.indicator_label.setObjectName("typing_indicator")
        self.indicator_label.setStyleSheet("""
            QLabel {
                color: #e2e8f0;
                font-size: 14px;
                background: transparent;
            }
        """)
        indicator_layout.addWidget(self.indicator_label)
        
        layout.addWidget(indicator_container)
        layout.addStretch()
    
    def setup_animation(self):
        """设置动画"""
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_dots)
        self.timer.start(500)  # 每500ms更新一次
    
    def update_dots(self):
        """更新点点动画"""
        self.dots[self.current_dot] = "⚪"
        self.current_dot = (self.current_dot + 1) % 3
        self.dots[self.current_dot] = "⚫"
        
        dots_text = " ".join(self.dots)
        self.indicator_label.setText(f"正在思考 {dots_text}")
    
    def stop_animation(self):
        """停止动画"""
        if hasattr(self, 'timer'):
            self.timer.stop()
            
    def start_typing(self):
        """开始显示打字动画"""
        self.show()
        if hasattr(self, 'timer') and not self.timer.isActive():
            self.timer.start(500)
            
    def stop_typing(self):
        """停止打字动画"""
        self.stop_animation()
        self.hide()

class ChatInput(QTextEdit):
    """聊天输入框"""
    
    message_sent = pyqtSignal(str)
    typing_started = pyqtSignal()
    typing_stopped = pyqtSignal()
    
    def __init__(self):
        super().__init__()
        self.typing_timer = QTimer()
        self.typing_timer.setSingleShot(True)
        self.typing_timer.timeout.connect(self.typing_stopped.emit)
        
        self.init_ui()
    
    def init_ui(self):
        """初始化UI"""
        self.setPlaceholderText("输入消息... (Ctrl+Enter发送)")
        self.setMaximumHeight(120)
        self.setMinimumHeight(40)
        
        # 设置字体
        font = QFont("Microsoft YaHei UI", 12)
        self.setFont(font)
        
        # 连接信号
        self.textChanged.connect(self.on_text_changed)
    
    def keyPressEvent(self, event):
        """按键事件"""
        if event.key() == Qt.Key.Key_Return or event.key() == Qt.Key.Key_Enter:
            if event.modifiers() == Qt.KeyboardModifier.ControlModifier:
                # Ctrl+Enter发送消息
                self.send_message()
                return
            elif event.modifiers() == Qt.KeyboardModifier.ShiftModifier:
                # Shift+Enter换行
                super().keyPressEvent(event)
                return
            else:
                # 普通Enter发送消息
                self.send_message()
                return
        
        super().keyPressEvent(event)
    
    def send_message(self):
        """发送消息"""
        text = self.toPlainText().strip()
        if text:
            self.message_sent.emit(text)
            self.clear()
    
    def on_text_changed(self):
        """文本变化事件"""
        if self.toPlainText().strip():
            self.typing_started.emit()
            self.typing_timer.start(1000)  # 1秒后触发停止输入
        else:
            self.typing_stopped.emit()

class ChatWidget(QWidget):
    """聊天组件"""
    
    message_sent = pyqtSignal(str)
    typing_started = pyqtSignal()
    typing_stopped = pyqtSignal()
    
    def __init__(self):
        super().__init__()
        self.messages = []
        self.current_ai_message = None
        self.typing_indicator = TypingIndicator()
        self.typing_indicator.hide()
        
        self.init_ui()
        self.setup_connections()
    
    def init_ui(self):
        """初始化UI"""
        self.setObjectName("chat_widget")
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(0)
        
        # 消息显示区域 - 改进滚动功能
        self.scroll_area = QScrollArea()
        self.scroll_area.setWidgetResizable(True)
        self.scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)
        self.scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)

        # 设置滚动区域样式
        self.scroll_area.setStyleSheet("""
            QScrollArea {
                background: transparent;
                border: none;
                border-radius: 0;
            }
            QScrollBar:vertical {
                background: rgba(45, 53, 72, 0.5);
                width: 12px;
                border-radius: 6px;
                margin: 0;
            }
            QScrollBar::handle:vertical {
                background: rgba(102, 126, 234, 0.6);
                border-radius: 6px;
                min-height: 20px;
                margin: 2px;
            }
            QScrollBar::handle:vertical:hover {
                background: rgba(102, 126, 234, 0.8);
            }
            QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
                height: 0;
                width: 0;
            }
            QScrollBar::add-page:vertical, QScrollBar::sub-page:vertical {
                background: transparent;
            }
        """)
        
        # 消息容器
        self.messages_widget = QWidget()
        self.messages_layout = QVBoxLayout(self.messages_widget)
        self.messages_layout.setContentsMargins(10, 10, 10, 10)
        self.messages_layout.setSpacing(8)

        # 添加打字指示器
        self.messages_layout.addWidget(self.typing_indicator)

        self.messages_layout.addStretch()  # 底部弹簧，让消息从底部开始
        
        self.scroll_area.setWidget(self.messages_widget)
        
        # 现代化输入区域
        input_frame = QFrame()
        input_frame.setObjectName("input_frame")
        input_frame.setStyleSheet("""
            QFrame#input_frame {
                background: rgba(26, 31, 46, 0.95);
                border: 1px solid rgba(102, 126, 234, 0.3);
                border-radius: 25px;
                margin: 15px;
                padding: 5px;
            }
        """)

        input_layout = QHBoxLayout(input_frame)
        input_layout.setContentsMargins(20, 15, 20, 15)
        input_layout.setSpacing(15)

        # 现代化输入框
        self.chat_input = ChatInput()
        self.chat_input.setObjectName("chat_input")
        self.chat_input.setStyleSheet("""
            QTextEdit#chat_input {
                background: transparent;
                border: none;
                border-radius: 15px;
                padding: 12px 16px;
                font-size: 14px;
                color: #e2e8f0;
                font-family: 'Microsoft YaHei UI', 'Segoe UI', sans-serif;
            }
            QTextEdit#chat_input:focus {
                background: rgba(45, 53, 72, 0.5);
                border: 2px solid rgba(102, 126, 234, 0.5);
            }
        """)
        input_layout.addWidget(self.chat_input, 1)

        # 现代化发送按钮
        self.send_button = QPushButton("发送 ✈️")
        self.send_button.setObjectName("send_button")
        self.send_button.setFixedSize(80, 45)
        self.send_button.setStyleSheet("""
            QPushButton#send_button {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #667eea, stop:1 #764ba2);
                color: white;
                border: none;
                border-radius: 22px;
                font-size: 13px;
                font-weight: 600;
                padding: 0;
            }
            QPushButton#send_button:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #7c3aed, stop:1 #06d6a0);
                border: 2px solid rgba(255,255,255,0.3);
            }
            QPushButton#send_button:pressed {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #5b21b6, stop:1 #059669);
            }
        """)
        self.send_button.clicked.connect(self.send_message)
        input_layout.addWidget(self.send_button)
        
        # 添加到主布局
        layout.addWidget(self.scroll_area, 1)
        layout.addWidget(input_frame)
    
    def setup_connections(self):
        """设置信号连接"""
        self.chat_input.message_sent.connect(self.message_sent.emit)
        self.chat_input.typing_started.connect(self.typing_started.emit)
        self.chat_input.typing_stopped.connect(self.typing_stopped.emit)
    
    def add_user_message(self, message: str):
        """添加用户消息"""
        bubble = MessageBubble(message, is_user=True)
        self.messages_layout.insertWidget(self.messages_layout.count() - 1, bubble)
        self.messages.append(bubble)
        self.scroll_to_bottom()
    
    def add_ai_message(self, message: str, avatar_path: str = None):
        """添加AI消息"""
        bubble = MessageBubble(message, is_user=False, avatar_path=avatar_path)
        self.messages_layout.insertWidget(self.messages_layout.count() - 1, bubble)
        self.messages.append(bubble)
        self.scroll_to_bottom()
    
    def start_ai_response(self):
        """开始AI回复"""
        # 确保之前的输入指示器已停止
        if self.typing_indicator:
            self.typing_indicator.stop_animation()
        else:
            # 创建新的输入指示器
            self.typing_indicator = TypingIndicator()
            self.messages_layout.insertWidget(self.messages_layout.count() - 1, self.typing_indicator)
        
        # 显示输入指示器
        self.typing_indicator.start_typing()
        self.scroll_to_bottom()
        
        # 准备AI消息容器
        self.current_ai_message = ""
        
        # 清除之前的AI消息气泡引用
        if hasattr(self, '_current_ai_bubble'):
            delattr(self, '_current_ai_bubble')
    
    def append_ai_response(self, chunk: str):
        """追加AI回复内容"""
        self.current_ai_message += chunk
        
        # 更新或创建AI消息气泡
        if hasattr(self, '_current_ai_bubble'):
            # 更新现有气泡
            self._current_ai_bubble.message = self.current_ai_message
            
            # 查找并更新AI消息标签
            for widget in self._current_ai_bubble.findChildren(QLabel):
                if widget.objectName() == "ai_message":
                    widget.setText(self.current_ai_message)
                    break
        else:
            # 移除输入指示器
            if self.typing_indicator:
                self.typing_indicator.stop_animation()
                self.typing_indicator.hide()
                
            # 创建新的AI消息气泡
            self._current_ai_bubble = MessageBubble(self.current_ai_message, is_user=False)
            self.messages_layout.insertWidget(self.messages_layout.count() - 1, self._current_ai_bubble)
        
        self.scroll_to_bottom()
    
    def finish_ai_response(self):
        """完成AI回复"""
        # 隐藏打字指示器
        if self.typing_indicator:
            self.typing_indicator.stop_typing()
        
        # 保存当前AI消息气泡到消息列表
        if hasattr(self, '_current_ai_bubble'):
            self.messages.append(self._current_ai_bubble)
            delattr(self, '_current_ai_bubble')
        
        self.current_ai_message = None
        self.scroll_to_bottom()
    
    def send_message(self):
        """发送消息"""
        text = self.chat_input.toPlainText().strip()
        if text:
            self.add_user_message(text)
            self.message_sent.emit(text)
    
    def clear_chat(self):
        """清空聊天"""
        for message in self.messages:
            self.messages_layout.removeWidget(message)
            message.deleteLater()
        
        self.messages.clear()
        
        if self.typing_indicator:
            self.typing_indicator.stop_animation()
            self.messages_layout.removeWidget(self.typing_indicator)
            self.typing_indicator.deleteLater()
            self.typing_indicator = None
    
    def load_conversation(self, context: List[Dict[str, Any]]):
        """加载对话历史"""
        self.clear_chat()
        
        for item in context:
            if item['role'] == 'user':
                self.add_user_message(item['content'])
            elif item['role'] == 'assistant':
                self.add_ai_message(item['content'])
    
    def scroll_to_bottom(self):
        """滚动到底部"""
        QTimer.singleShot(10, lambda: self.scroll_area.verticalScrollBar().setValue(
            self.scroll_area.verticalScrollBar().maximum()))
    
    def set_focus_to_input(self):
        """设置焦点到输入框"""
        self.chat_input.setFocus()
    
    def add_error_message(self, error: str):
        """添加错误消息"""
        # 创建一个特殊的错误消息气泡
        error_frame = QFrame()
        error_frame.setObjectName("error_message_frame")
        error_frame.setStyleSheet("""
            QFrame#error_message_frame {
                background: rgba(220, 38, 38, 0.2);
                border: 1px solid rgba(220, 38, 38, 0.5);
                border-radius: 18px;
                margin: 5px 20px;
            }
        """)
        
        error_layout = QVBoxLayout(error_frame)
        error_layout.setContentsMargins(16, 12, 16, 12)
        
        error_label = QLabel(f"❌ {error}")
        error_label.setObjectName("error_message")
        error_label.setWordWrap(True)
        error_label.setTextInteractionFlags(Qt.TextInteractionFlag.TextSelectableByMouse)
        error_label.setStyleSheet("""
            QLabel#error_message {
                color: #f87171;
                font-size: 14px;
                line-height: 1.6;
                background: transparent;
            }
        """)
        error_layout.addWidget(error_label)
        
        self.messages_layout.insertWidget(self.messages_layout.count() - 1, error_frame)
        self.messages.append(error_frame)
        self.scroll_to_bottom()

    def show_typing_indicator(self):
        """显示打字指示器"""
        self.typing_indicator.start_typing()
        self.scroll_to_bottom()

    def hide_typing_indicator(self):
        """隐藏打字指示器"""
        self.typing_indicator.stop_typing()
    
    def apply_theme(self, theme):
        """应用主题"""
        self.setStyleSheet(theme.get_chat_stylesheet())

    def update_token_count(self, count: int):
        """更新Token计数"""
        if hasattr(self, 'token_counter'):
            if count > 1000:
                self.token_counter.setText(f"Tokens: {count:,}")
                self.token_counter.setStyleSheet("""
                    QLabel {
                        color: #f59e0b;
                        font-size: 11px;
                        font-weight: 500;
                        background: rgba(245, 158, 11, 0.1);
                        border: none;
                        padding: 4px 8px;
                        border-radius: 12px;
                    }
                """)
            else:
                self.token_counter.setText(f"Tokens: {count}")
                self.token_counter.setStyleSheet("""
                    QLabel {
                        color: #64748b;
                        font-size: 11px;
                        font-weight: 500;
                        background: transparent;
                        border: none;
                        padding: 4px 8px;
                        border-radius: 12px;
                    }
                """)

    def update_persona_indicator(self, persona_name: str):
        """更新人设指示器"""
        if hasattr(self, 'persona_indicator'):
            self.persona_indicator.setText(f"💬 {persona_name}")
            self.persona_indicator.setStyleSheet("""
                QLabel {
                    color: #64748b;
                    font-size: 11px;
                    font-weight: 500;
                    background: rgba(100, 116, 139, 0.1);
                    border: none;
                    padding: 4px 8px;
                    border-radius: 12px;
                }
            """)
