2025-08-01 15:16:00 | INFO     | core.utils.config:load_config:36 | 配置文件加载成功
2025-08-01 15:16:03 | INFO     | core.llm.model_manager:_load_config:56 | 配置文件加载成功
2025-08-01 15:16:03 | INFO     | core.llm.model_manager:_scan_models:72 | 发现模型: lucy_128k-Q8_0
2025-08-01 15:18:48 | INFO     | core.utils.config:load_config:36 | 配置文件加载成功
2025-08-01 15:18:48 | INFO     | personas.persona_manager:_load_personas:61 | 加载人设: 职场导师 (career_mentor)
2025-08-01 15:18:48 | INFO     | personas.persona_manager:_load_personas:61 | 加载人设: 推理专家 (reasoning_expert)
2025-08-01 15:18:48 | INFO     | personas.persona_manager:_load_personas:61 | 加载人设: 旅行向导 (travel_guide)
2025-08-01 15:18:48 | INFO     | personas.persona_manager:_load_personas:61 | 加载人设: 妻子模式 (wife)
2025-08-01 15:18:48 | INFO     | personas.persona_manager:switch_persona:107 | 切换到人设: 职场导师
2025-08-01 15:18:48 | INFO     | memory.context_engine:start_session:252 | 开始新会话: test_persona_20250801_151848
2025-08-01 15:18:48 | INFO     | core.llm.model_manager:_load_config:56 | 配置文件加载成功
2025-08-01 15:18:48 | INFO     | core.llm.model_manager:_scan_models:72 | 发现模型: lucy_128k-Q8_0
2025-08-01 15:20:29 | INFO     | core.utils.config:load_config:36 | 配置文件加载成功
2025-08-01 15:20:29 | INFO     | personas.persona_manager:_load_personas:61 | 加载人设: 职场导师 (career_mentor)
2025-08-01 15:20:29 | INFO     | personas.persona_manager:_load_personas:61 | 加载人设: 推理专家 (reasoning_expert)
2025-08-01 15:20:29 | INFO     | personas.persona_manager:_load_personas:61 | 加载人设: 旅行向导 (travel_guide)
2025-08-01 15:20:29 | INFO     | personas.persona_manager:_load_personas:61 | 加载人设: 妻子模式 (wife)
2025-08-01 15:20:29 | INFO     | core.llm.model_manager:_load_config:56 | 配置文件加载成功
2025-08-01 15:20:29 | INFO     | core.llm.model_manager:_scan_models:72 | 发现模型: lucy_128k-Q8_0
2025-08-01 15:21:07 | INFO     | engine_2d.image_generator:__init__:79 | 2D Engine初始化完成，设备: cuda
2025-08-01 15:21:07 | INFO     | engine_2d.image_generator:__init__:80 | 发现 0 个可用模型
2025-08-01 15:21:07 | INFO     | personas.persona_manager:switch_persona:107 | 切换到人设: 职场导师
2025-08-01 15:21:07 | INFO     | memory.context_engine:start_session:252 | 开始新会话: test_persona_20250801_152107
2025-08-01 15:21:07 | INFO     | memory.context_engine:add_memory:188 | 添加记忆: 7d2d27a8... (重要性: 0.70)
2025-08-01 15:21:07 | INFO     | memory.context_engine:add_memory:188 | 添加记忆: 252fe1d5... (重要性: 0.50)
2025-08-01 15:22:07 | INFO     | core.llm.model_manager:_load_config:56 | 配置文件加载成功
2025-08-01 15:22:07 | INFO     | core.llm.model_manager:_scan_models:72 | 发现模型: lucy_128k-Q8_0
2025-08-01 15:22:07 | INFO     | core.llm.model_manager:load_model:112 | 正在加载模型: lucy_128k-Q8_0
2025-08-01 15:22:10 | INFO     | core.llm.model_manager:load_model:122 | 模型加载成功: lucy_128k-Q8_0
2025-08-01 15:22:10 | INFO     | core.utils.config:load_config:36 | 配置文件加载成功
2025-08-01 15:22:15 | INFO     | engine_2d.image_generator:__init__:79 | 2D Engine初始化完成，设备: cuda
2025-08-01 15:22:15 | INFO     | engine_2d.image_generator:__init__:80 | 发现 0 个可用模型
2025-08-01 15:22:15 | INFO     | personas.persona_manager:_load_personas:61 | 加载人设: 职场导师 (career_mentor)
2025-08-01 15:22:15 | INFO     | personas.persona_manager:_load_personas:61 | 加载人设: 推理专家 (reasoning_expert)
2025-08-01 15:22:15 | INFO     | personas.persona_manager:_load_personas:61 | 加载人设: 旅行向导 (travel_guide)
2025-08-01 15:22:15 | INFO     | personas.persona_manager:_load_personas:61 | 加载人设: 妻子模式 (wife)
2025-08-01 15:22:15 | INFO     | personas.persona_manager:switch_persona:107 | 切换到人设: 妻子模式
2025-08-01 15:22:15 | ERROR    | core.ai_engine:generate_response:65 | AI回复生成失败: 'SearchManager' object has no attribute 'search'
2025-08-01 15:24:51 | INFO     | core.llm.model_manager:_load_config:56 | 配置文件加载成功
2025-08-01 15:24:51 | INFO     | core.llm.model_manager:_scan_models:72 | 发现模型: lucy_128k-Q8_0
2025-08-01 15:24:51 | INFO     | core.llm.model_manager:load_model:112 | 正在加载模型: lucy_128k-Q8_0
2025-08-01 15:24:54 | INFO     | core.llm.model_manager:load_model:122 | 模型加载成功: lucy_128k-Q8_0
2025-08-01 15:24:59 | INFO     | core.utils.config:load_config:36 | 配置文件加载成功
2025-08-01 15:25:05 | INFO     | engine_2d.image_generator:__init__:79 | 2D Engine初始化完成，设备: cuda
2025-08-01 15:25:05 | INFO     | engine_2d.image_generator:__init__:80 | 发现 0 个可用模型
2025-08-01 15:25:05 | INFO     | personas.persona_manager:_load_personas:61 | 加载人设: 职场导师 (career_mentor)
2025-08-01 15:25:05 | INFO     | personas.persona_manager:_load_personas:61 | 加载人设: 推理专家 (reasoning_expert)
2025-08-01 15:25:05 | INFO     | personas.persona_manager:_load_personas:61 | 加载人设: 旅行向导 (travel_guide)
2025-08-01 15:25:05 | INFO     | personas.persona_manager:_load_personas:61 | 加载人设: 妻子模式 (wife)
2025-08-01 15:25:05 | INFO     | personas.persona_manager:switch_persona:107 | 切换到人设: 妻子模式
2025-08-01 15:25:05 | INFO     | search.search_manager:search_with_context:98 | 执行搜索: 亲爱的，我今天工作很累
2025-08-01 15:25:05 | INFO     | search.engines.duckduckgo_search:search:55 | 搜索查询: 亲爱的，我今天工作很累
2025-08-01 15:25:06 | INFO     | search.engines.duckduckgo_search:_search_with_ddgs:81 | 搜索完成，找到 5 个结果
2025-08-01 15:25:25 | ERROR    | search.engines.duckduckgo_search:get_page_content:164 | 获取网页内容失败: 403 Client Error: Forbidden for url: https://zhuanlan.zhihu.com/p/30178868633
2025-08-01 15:28:40 | INFO     | core.llm.model_manager:_load_config:56 | 配置文件加载成功
2025-08-01 15:28:40 | INFO     | core.llm.model_manager:_scan_models:72 | 发现模型: lucy_128k-Q8_0
2025-08-01 15:28:40 | INFO     | core.utils.config:load_config:36 | 配置文件加载成功
2025-08-01 15:28:43 | INFO     | engine_2d.image_generator:__init__:79 | 2D Engine初始化完成，设备: cuda
2025-08-01 15:28:43 | INFO     | engine_2d.image_generator:__init__:80 | 发现 0 个可用模型
2025-08-01 15:28:43 | INFO     | personas.persona_manager:_load_personas:61 | 加载人设: 职场导师 (career_mentor)
2025-08-01 15:28:43 | INFO     | personas.persona_manager:_load_personas:61 | 加载人设: 推理专家 (reasoning_expert)
2025-08-01 15:28:43 | INFO     | personas.persona_manager:_load_personas:61 | 加载人设: 旅行向导 (travel_guide)
2025-08-01 15:28:43 | INFO     | personas.persona_manager:_load_personas:61 | 加载人设: 妻子模式 (wife)
2025-08-01 15:28:43 | INFO     | personas.persona_manager:switch_persona:107 | 切换到人设: 妻子模式
2025-08-01 15:30:40 | INFO     | core.utils.config:load_config:36 | 配置文件加载成功
2025-08-01 15:30:40 | INFO     | core.llm.model_manager:_load_config:56 | 配置文件加载成功
2025-08-01 15:30:40 | INFO     | core.llm.model_manager:_scan_models:72 | 发现模型: lucy_128k-Q8_0
2025-08-01 15:30:43 | INFO     | engine_2d.image_generator:__init__:79 | 2D Engine初始化完成，设备: cuda
2025-08-01 15:30:43 | INFO     | engine_2d.image_generator:__init__:80 | 发现 0 个可用模型
2025-08-01 15:30:43 | INFO     | personas.persona_manager:_load_personas:61 | 加载人设: 职场导师 (career_mentor)
2025-08-01 15:30:43 | INFO     | personas.persona_manager:_load_personas:61 | 加载人设: 推理专家 (reasoning_expert)
2025-08-01 15:30:43 | INFO     | personas.persona_manager:_load_personas:61 | 加载人设: 旅行向导 (travel_guide)
2025-08-01 15:30:43 | INFO     | personas.persona_manager:_load_personas:61 | 加载人设: 妻子模式 (wife)
2025-08-01 15:30:43 | INFO     | personas.persona_manager:switch_persona:107 | 切换到人设: 职场导师
2025-08-01 15:30:43 | INFO     | memory.context_engine:start_session:252 | 开始新会话: career_mentor_20250801_153043
2025-08-01 15:30:43 | INFO     | ui.web.app:start_new_session:51 | 开始新会话: career_mentor_20250801_153043
2025-08-01 15:30:43 | INFO     | personas.persona_manager:switch_persona:107 | 切换到人设: 妻子模式
2025-08-01 15:33:43 | INFO     | core.utils.config:load_config:36 | 配置文件加载成功
2025-08-01 15:33:43 | INFO     | core.llm.model_manager:_load_config:56 | 配置文件加载成功
2025-08-01 15:33:43 | INFO     | core.llm.model_manager:_scan_models:72 | 发现模型: lucy_128k-Q8_0
2025-08-01 15:33:43 | INFO     | personas.persona_manager:_load_personas:61 | 加载人设: 职场导师 (career_mentor)
2025-08-01 15:33:43 | INFO     | personas.persona_manager:_load_personas:61 | 加载人设: 推理专家 (reasoning_expert)
2025-08-01 15:33:43 | INFO     | personas.persona_manager:_load_personas:61 | 加载人设: 旅行向导 (travel_guide)
2025-08-01 15:33:43 | INFO     | personas.persona_manager:_load_personas:61 | 加载人设: 妻子模式 (wife)
2025-08-01 15:33:46 | INFO     | engine_2d.image_generator:__init__:79 | 2D Engine初始化完成，设备: cuda
2025-08-01 15:33:46 | INFO     | engine_2d.image_generator:__init__:80 | 发现 0 个可用模型
2025-08-01 15:33:46 | ERROR    | ui.app.components.sidebar:load_chat_history:204 | 加载对话历史失败: 'ContextEngine' object has no attribute 'get_all_sessions'
2025-08-01 15:33:47 | INFO     | ui.app.main:__init__:45 | Reverie Agents 桌面应用启动成功
2025-08-01 15:33:47 | INFO     | core.utils.config:save_config:164 | 配置文件保存成功
2025-08-01 15:33:47 | INFO     | ui.app.main:closeEvent:260 | 应用程序关闭
2025-08-01 15:33:47 | INFO     | personas.persona_manager:switch_persona:107 | 切换到人设: 职场导师
2025-08-01 15:33:47 | INFO     | memory.context_engine:start_session:252 | 开始新会话: career_mentor_20250801_153347
2025-08-01 15:33:47 | INFO     | ui.web.app:start_new_session:51 | 开始新会话: career_mentor_20250801_153347
2025-08-01 15:33:47 | INFO     | personas.persona_manager:switch_persona:107 | 切换到人设: 妻子模式
2025-08-01 15:35:40 | INFO     | core.utils.config:load_config:36 | 配置文件加载成功
2025-08-01 15:35:40 | INFO     | personas.persona_manager:_load_personas:61 | 加载人设: 职场导师 (career_mentor)
2025-08-01 15:35:40 | INFO     | personas.persona_manager:_load_personas:61 | 加载人设: 推理专家 (reasoning_expert)
2025-08-01 15:35:40 | INFO     | personas.persona_manager:_load_personas:61 | 加载人设: 旅行向导 (travel_guide)
2025-08-01 15:35:40 | INFO     | personas.persona_manager:_load_personas:61 | 加载人设: 妻子模式 (wife)
2025-08-01 15:35:40 | INFO     | personas.persona_manager:switch_persona:107 | 切换到人设: 妻子模式
2025-08-01 15:35:40 | INFO     | personas.persona_manager:switch_persona:107 | 切换到人设: 职场导师
2025-08-01 15:35:40 | INFO     | personas.persona_manager:switch_persona:107 | 切换到人设: 推理专家
2025-08-01 15:35:40 | INFO     | personas.persona_manager:switch_persona:107 | 切换到人设: 妻子模式
2025-08-01 15:35:40 | INFO     | memory.context_engine:start_session:252 | 开始新会话: wife_20250801_153540
2025-08-01 15:35:40 | INFO     | memory.context_engine:add_memory:188 | 添加记忆: c7d371a9... (重要性: 0.70)
2025-08-01 15:35:40 | INFO     | memory.context_engine:add_memory:188 | 添加记忆: cee443ae... (重要性: 0.50)
2025-08-01 15:35:40 | INFO     | memory.context_engine:add_memory:188 | 添加记忆: e19f71eb... (重要性: 0.70)
2025-08-01 15:35:40 | INFO     | memory.context_engine:add_memory:188 | 添加记忆: 8ead190a... (重要性: 0.60)
2025-08-01 15:35:40 | INFO     | memory.context_engine:add_memory:188 | 添加记忆: b3c0ad6f... (重要性: 0.70)
2025-08-01 15:35:40 | INFO     | memory.context_engine:add_memory:188 | 添加记忆: 2fdeb23d... (重要性: 0.50)
2025-08-01 15:35:40 | INFO     | search.search_manager:search_with_context:98 | 执行搜索: Python编程语言特点
2025-08-01 15:35:40 | INFO     | search.engines.duckduckgo_search:search:55 | 搜索查询: Python编程语言特点
2025-08-01 15:35:41 | INFO     | search.engines.duckduckgo_search:_search_with_ddgs:81 | 搜索完成，找到 5 个结果
2025-08-01 15:35:46 | ERROR    | search.engines.duckduckgo_search:get_page_content:164 | 获取网页内容失败: 403 Client Error: Forbidden for url: https://ticket.louvre.fr/
2025-08-01 15:36:03 | INFO     | core.llm.model_manager:_load_config:56 | 配置文件加载成功
2025-08-01 15:36:03 | INFO     | core.llm.model_manager:_scan_models:72 | 发现模型: lucy_128k-Q8_0
2025-08-01 15:36:07 | INFO     | engine_2d.image_generator:__init__:79 | 2D Engine初始化完成，设备: cuda
2025-08-01 15:36:07 | INFO     | engine_2d.image_generator:__init__:80 | 发现 0 个可用模型
2025-08-01 15:36:07 | INFO     | personas.persona_manager:switch_persona:107 | 切换到人设: 妻子模式
2025-08-01 15:36:07 | INFO     | search.search_manager:search_with_context:98 | 执行搜索: 亲爱的，听你说说今天的心情
2025-08-01 15:36:07 | INFO     | search.engines.duckduckgo_search:search:55 | 搜索查询: 亲爱的，听你说说今天的心情
2025-08-01 15:36:09 | INFO     | search.engines.duckduckgo_search:_search_with_ddgs:81 | 搜索完成，找到 5 个结果
2025-08-01 15:39:22 | INFO     | core.utils.config:load_config:36 | 配置文件加载成功
2025-08-01 15:39:22 | INFO     | personas.persona_manager:_load_personas:61 | 加载人设: 职场导师 (career_mentor)
2025-08-01 15:39:22 | INFO     | personas.persona_manager:_load_personas:61 | 加载人设: 推理专家 (reasoning_expert)
2025-08-01 15:39:22 | INFO     | personas.persona_manager:_load_personas:61 | 加载人设: 旅行向导 (travel_guide)
2025-08-01 15:39:22 | INFO     | personas.persona_manager:_load_personas:61 | 加载人设: 妻子模式 (wife)
2025-08-01 15:39:22 | INFO     | personas.persona_manager:switch_persona:107 | 切换到人设: 妻子模式
2025-08-01 15:39:22 | INFO     | personas.persona_manager:switch_persona:107 | 切换到人设: 职场导师
2025-08-01 15:39:22 | INFO     | personas.persona_manager:switch_persona:107 | 切换到人设: 推理专家
2025-08-01 15:39:22 | INFO     | personas.persona_manager:switch_persona:107 | 切换到人设: 妻子模式
2025-08-01 15:39:22 | INFO     | memory.context_engine:start_session:252 | 开始新会话: wife_20250801_153922
2025-08-01 15:39:22 | INFO     | memory.context_engine:add_memory:188 | 添加记忆: 307030b8... (重要性: 0.70)
2025-08-01 15:39:22 | INFO     | memory.context_engine:add_memory:188 | 添加记忆: 938d48dd... (重要性: 0.50)
2025-08-01 15:39:22 | INFO     | memory.context_engine:add_memory:188 | 添加记忆: 2b05f27c... (重要性: 0.70)
2025-08-01 15:39:22 | INFO     | memory.context_engine:add_memory:188 | 添加记忆: 1f9ce09e... (重要性: 0.60)
2025-08-01 15:39:22 | INFO     | memory.context_engine:add_memory:188 | 添加记忆: fbe72bf2... (重要性: 0.70)
2025-08-01 15:39:22 | INFO     | memory.context_engine:add_memory:188 | 添加记忆: 836862df... (重要性: 0.50)
2025-08-01 15:39:22 | INFO     | search.search_manager:search_with_context:98 | 执行搜索: Python编程语言特点
2025-08-01 15:39:22 | INFO     | search.engines.duckduckgo_search:search:55 | 搜索查询: Python编程语言特点
2025-08-01 15:39:24 | INFO     | search.engines.duckduckgo_search:_search_with_ddgs:81 | 搜索完成，找到 0 个结果
2025-08-01 15:39:24 | INFO     | core.llm.model_manager:_load_config:56 | 配置文件加载成功
2025-08-01 15:39:24 | INFO     | core.llm.model_manager:_scan_models:72 | 发现模型: lucy_128k-Q8_0
2025-08-01 15:39:27 | INFO     | engine_2d.image_generator:__init__:79 | 2D Engine初始化完成，设备: cuda
2025-08-01 15:39:27 | INFO     | engine_2d.image_generator:__init__:80 | 发现 0 个可用模型
2025-08-01 15:39:27 | INFO     | personas.persona_manager:switch_persona:107 | 切换到人设: 妻子模式
2025-08-01 15:39:27 | INFO     | search.search_manager:search_with_context:98 | 执行搜索: 亲爱的，听你说说今天的心情
2025-08-01 15:39:27 | INFO     | search.engines.duckduckgo_search:search:55 | 搜索查询: 亲爱的，听你说说今天的心情
2025-08-01 15:39:29 | INFO     | search.engines.duckduckgo_search:_search_with_ddgs:81 | 搜索完成，找到 5 个结果
2025-08-01 15:39:34 | ERROR    | search.engines.duckduckgo_search:get_page_content:164 | 获取网页内容失败: 503 Server Error: Service Unavailable for url: https://beta.finance.yahoo.com/quote/AMZN/
2025-08-01 15:39:35 | ERROR    | search.engines.duckduckgo_search:get_page_content:164 | 获取网页内容失败: 503 Server Error: Service Unavailable for url: https://finance.yahoo.com/quotes/AMZN/
2025-08-01 15:39:37 | ERROR    | search.engines.duckduckgo_search:get_page_content:164 | 获取网页内容失败: 503 Server Error: Service Unavailable for url: https://finance.yahoo.com/quote/AMZN/analysis/
2025-08-01 15:39:38 | ERROR    | search.engines.duckduckgo_search:get_page_content:164 | 获取网页内容失败: 503 Server Error: Service Unavailable for url: https://finance.yahoo.com/chart/AMZN
2025-08-01 15:40:29 | INFO     | core.utils.config:load_config:36 | 配置文件加载成功
2025-08-01 15:40:29 | INFO     | personas.persona_manager:_load_personas:61 | 加载人设: 职场导师 (career_mentor)
2025-08-01 15:40:29 | INFO     | personas.persona_manager:_load_personas:61 | 加载人设: 推理专家 (reasoning_expert)
2025-08-01 15:40:29 | INFO     | personas.persona_manager:_load_personas:61 | 加载人设: 旅行向导 (travel_guide)
2025-08-01 15:40:29 | INFO     | personas.persona_manager:_load_personas:61 | 加载人设: 妻子模式 (wife)
2025-08-01 15:40:29 | INFO     | personas.persona_manager:switch_persona:107 | 切换到人设: 妻子模式
2025-08-01 15:40:29 | INFO     | personas.persona_manager:switch_persona:107 | 切换到人设: 职场导师
2025-08-01 15:40:29 | INFO     | personas.persona_manager:switch_persona:107 | 切换到人设: 推理专家
2025-08-01 15:40:29 | INFO     | personas.persona_manager:switch_persona:107 | 切换到人设: 妻子模式
2025-08-01 15:40:29 | INFO     | memory.context_engine:start_session:252 | 开始新会话: wife_20250801_154029
2025-08-01 15:40:29 | INFO     | memory.context_engine:add_memory:188 | 添加记忆: b9268da2... (重要性: 0.70)
2025-08-01 15:40:29 | INFO     | memory.context_engine:add_memory:188 | 添加记忆: 8c235f38... (重要性: 0.50)
2025-08-01 15:40:29 | INFO     | memory.context_engine:add_memory:188 | 添加记忆: fecf71c6... (重要性: 0.70)
2025-08-01 15:40:29 | INFO     | memory.context_engine:add_memory:188 | 添加记忆: e8d0c9ed... (重要性: 0.60)
2025-08-01 15:40:29 | INFO     | memory.context_engine:add_memory:188 | 添加记忆: 1ffdb02a... (重要性: 0.70)
2025-08-01 15:40:29 | INFO     | memory.context_engine:add_memory:188 | 添加记忆: de8cd1a8... (重要性: 0.50)
2025-08-01 15:40:29 | INFO     | search.search_manager:search_with_context:98 | 执行搜索: Python编程语言特点
2025-08-01 15:40:29 | INFO     | search.engines.duckduckgo_search:search:55 | 搜索查询: Python编程语言特点
2025-08-01 15:40:32 | INFO     | search.engines.duckduckgo_search:_search_with_ddgs:81 | 搜索完成，找到 5 个结果
2025-08-01 15:40:52 | ERROR    | search.engines.duckduckgo_search:get_page_content:164 | 获取网页内容失败: 403 Client Error: Forbidden for url: https://zhuanlan.zhihu.com/p/103033552
2025-08-01 15:40:54 | ERROR    | search.engines.duckduckgo_search:get_page_content:164 | 获取网页内容失败: 403 Client Error: Forbidden for url: https://www.zhihu.com/question/566203859
2025-08-01 15:40:59 | ERROR    | search.engines.duckduckgo_search:get_page_content:164 | 获取网页内容失败: 403 Client Error: Forbidden for url: https://zhuanlan.zhihu.com/p/178490624
2025-08-01 15:41:00 | INFO     | core.llm.model_manager:_load_config:56 | 配置文件加载成功
2025-08-01 15:41:00 | INFO     | core.llm.model_manager:_scan_models:72 | 发现模型: lucy_128k-Q8_0
2025-08-01 15:41:03 | INFO     | engine_2d.image_generator:__init__:79 | 2D Engine初始化完成，设备: cuda
2025-08-01 15:41:03 | INFO     | engine_2d.image_generator:__init__:80 | 发现 0 个可用模型
2025-08-01 15:41:03 | INFO     | personas.persona_manager:switch_persona:107 | 切换到人设: 妻子模式
2025-08-01 15:41:03 | INFO     | search.search_manager:search_with_context:98 | 执行搜索: 亲爱的，听你说说今天的心情
2025-08-01 15:41:03 | INFO     | search.engines.duckduckgo_search:search:55 | 搜索查询: 亲爱的，听你说说今天的心情
2025-08-01 15:41:05 | INFO     | search.engines.duckduckgo_search:_search_with_ddgs:81 | 搜索完成，找到 5 个结果
2025-08-01 15:41:14 | ERROR    | search.engines.duckduckgo_search:get_page_content:164 | 获取网页内容失败: 403 Client Error: Forbidden for url: https://zhuanlan.zhihu.com/p/27117691
2025-08-01 16:14:13 | INFO     | core.utils.config:load_config:36 | 配置文件加载成功
2025-08-01 16:14:13 | INFO     | core.llm.model_manager:_load_config:56 | 配置文件加载成功
2025-08-01 16:14:13 | INFO     | core.llm.model_manager:_scan_models:72 | 发现模型: lucy_128k-Q8_0
2025-08-01 16:14:14 | INFO     | personas.persona_manager:_load_personas:61 | 加载人设: 职场导师 (career_mentor)
2025-08-01 16:14:14 | INFO     | personas.persona_manager:_load_personas:61 | 加载人设: 推理专家 (reasoning_expert)
2025-08-01 16:14:14 | INFO     | personas.persona_manager:_load_personas:61 | 加载人设: 旅行向导 (travel_guide)
2025-08-01 16:14:14 | INFO     | personas.persona_manager:_load_personas:61 | 加载人设: 妻子模式 (wife)
2025-08-01 16:14:14 | ERROR    | ui.app.components.sidebar:load_chat_history:204 | 加载对话历史失败: 'ContextEngine' object has no attribute 'get_all_sessions'
2025-08-01 16:14:14 | INFO     | __main__:__init__:45 | Reverie Agents 桌面应用启动成功
2025-08-01 16:14:18 | INFO     | core.utils.config:save_config:164 | 配置文件保存成功
2025-08-01 16:14:18 | INFO     | __main__:closeEvent:260 | 应用程序关闭
2025-08-01 16:15:55 | INFO     | core.utils.config:load_config:36 | 配置文件加载成功
2025-08-01 16:15:55 | INFO     | core.llm.model_manager:_load_config:56 | 配置文件加载成功
2025-08-01 16:15:55 | INFO     | core.llm.model_manager:_scan_models:72 | 发现模型: lucy_128k-Q8_0
2025-08-01 16:15:55 | INFO     | personas.persona_manager:_load_personas:61 | 加载人设: 职场导师 (career_mentor)
2025-08-01 16:15:55 | INFO     | personas.persona_manager:_load_personas:61 | 加载人设: 推理专家 (reasoning_expert)
2025-08-01 16:15:55 | INFO     | personas.persona_manager:_load_personas:61 | 加载人设: 旅行向导 (travel_guide)
2025-08-01 16:15:55 | INFO     | personas.persona_manager:_load_personas:61 | 加载人设: 妻子模式 (wife)
2025-08-01 16:15:55 | INFO     | __main__:__init__:45 | Reverie Agents 桌面应用启动成功
2025-08-01 16:15:58 | INFO     | memory.context_engine:load_session:358 | 会话加载成功: test_persona_20250801_151848
2025-08-01 16:15:58 | INFO     | __main__:load_chat:170 | 加载对话: test_persona_20250801_151848
2025-08-01 16:15:59 | INFO     | memory.context_engine:load_session:358 | 会话加载成功: test_persona_20250801_152107
2025-08-01 16:15:59 | INFO     | __main__:load_chat:170 | 加载对话: test_persona_20250801_152107
2025-08-01 16:16:01 | INFO     | core.utils.config:save_config:164 | 配置文件保存成功
2025-08-01 16:16:01 | INFO     | __main__:closeEvent:260 | 应用程序关闭
2025-08-01 16:16:56 | INFO     | core.utils.config:load_config:36 | 配置文件加载成功
2025-08-01 16:16:56 | INFO     | core.llm.model_manager:_load_config:56 | 配置文件加载成功
2025-08-01 16:16:56 | INFO     | core.llm.model_manager:_scan_models:72 | 发现模型: lucy_128k-Q8_0
2025-08-01 16:16:56 | INFO     | personas.persona_manager:_load_personas:61 | 加载人设: 职场导师 (career_mentor)
2025-08-01 16:16:56 | INFO     | personas.persona_manager:_load_personas:61 | 加载人设: 推理专家 (reasoning_expert)
2025-08-01 16:16:56 | INFO     | personas.persona_manager:_load_personas:61 | 加载人设: 旅行向导 (travel_guide)
2025-08-01 16:16:56 | INFO     | personas.persona_manager:_load_personas:61 | 加载人设: 妻子模式 (wife)
2025-08-01 16:16:56 | INFO     | __main__:__init__:45 | Reverie Agents 桌面应用启动成功
2025-08-01 16:16:59 | INFO     | memory.context_engine:load_session:358 | 会话加载成功: test_persona_20250801_151848
2025-08-01 16:16:59 | INFO     | __main__:load_chat:170 | 加载对话: test_persona_20250801_151848
2025-08-01 16:16:59 | INFO     | memory.context_engine:load_session:358 | 会话加载成功: test_persona_20250801_151848
2025-08-01 16:16:59 | INFO     | __main__:load_chat:170 | 加载对话: test_persona_20250801_151848
2025-08-01 16:17:01 | INFO     | core.utils.config:save_config:164 | 配置文件保存成功
2025-08-01 16:17:01 | INFO     | __main__:closeEvent:260 | 应用程序关闭
2025-08-01 16:17:18 | INFO     | core.utils.config:load_config:36 | 配置文件加载成功
2025-08-01 16:17:18 | INFO     | core.llm.model_manager:_load_config:56 | 配置文件加载成功
2025-08-01 16:17:18 | INFO     | core.llm.model_manager:_scan_models:72 | 发现模型: lucy_128k-Q8_0
2025-08-01 16:17:38 | INFO     | engine_2d.image_generator:__init__:79 | 2D Engine初始化完成，设备: cuda
2025-08-01 16:17:38 | INFO     | engine_2d.image_generator:__init__:80 | 发现 0 个可用模型
2025-08-01 16:17:38 | INFO     | personas.persona_manager:_load_personas:61 | 加载人设: 职场导师 (career_mentor)
2025-08-01 16:17:38 | INFO     | personas.persona_manager:_load_personas:61 | 加载人设: 推理专家 (reasoning_expert)
2025-08-01 16:17:38 | INFO     | personas.persona_manager:_load_personas:61 | 加载人设: 旅行向导 (travel_guide)
2025-08-01 16:17:38 | INFO     | personas.persona_manager:_load_personas:61 | 加载人设: 妻子模式 (wife)
2025-08-01 16:17:38 | INFO     | __main__:main:401 | 启动Reverie Agents Web界面
2025-08-01 16:17:38 | INFO     | personas.persona_manager:switch_persona:107 | 切换到人设: 职场导师
2025-08-01 16:17:38 | INFO     | memory.context_engine:start_session:252 | 开始新会话: career_mentor_20250801_161738
2025-08-01 16:17:38 | INFO     | __main__:start_new_session:51 | 开始新会话: career_mentor_20250801_161738
2025-08-01 16:19:11 | INFO     | core.utils.config:load_config:36 | 配置文件加载成功
2025-08-01 16:19:11 | INFO     | core.llm.model_manager:_load_config:56 | 配置文件加载成功
2025-08-01 16:19:11 | INFO     | core.llm.model_manager:_scan_models:72 | 发现模型: lucy_128k-Q8_0
2025-08-01 16:19:14 | INFO     | engine_2d.image_generator:__init__:79 | 2D Engine初始化完成，设备: cuda
2025-08-01 16:19:14 | INFO     | engine_2d.image_generator:__init__:80 | 发现 0 个可用模型
2025-08-01 16:19:14 | INFO     | personas.persona_manager:_load_personas:61 | 加载人设: 职场导师 (career_mentor)
2025-08-01 16:19:14 | INFO     | personas.persona_manager:_load_personas:61 | 加载人设: 推理专家 (reasoning_expert)
2025-08-01 16:19:14 | INFO     | personas.persona_manager:_load_personas:61 | 加载人设: 旅行向导 (travel_guide)
2025-08-01 16:19:14 | INFO     | personas.persona_manager:_load_personas:61 | 加载人设: 妻子模式 (wife)
2025-08-01 16:19:14 | INFO     | __main__:main:401 | 启动Reverie Agents Web界面
2025-08-01 16:19:14 | INFO     | personas.persona_manager:switch_persona:107 | 切换到人设: 职场导师
2025-08-01 16:19:14 | INFO     | memory.context_engine:start_session:252 | 开始新会话: career_mentor_20250801_161914
2025-08-01 16:19:14 | INFO     | __main__:start_new_session:51 | 开始新会话: career_mentor_20250801_161914
2025-08-01 16:20:05 | ERROR    | __main__:main:91 | Web界面启动失败: Couldn't start the app because 'http://127.0.0.1:7861/gradio_api/startup-events' failed (code 502). Check your network or proxy settings to ensure localhost is accessible.
2025-08-01 16:20:28 | ERROR    | __main__:main:91 | Web界面启动失败: Couldn't start the app because 'http://127.0.0.1:7860/gradio_api/startup-events' failed (code 502). Check your network or proxy settings to ensure localhost is accessible.
2025-08-01 16:21:19 | INFO     | core.utils.config:load_config:36 | 配置文件加载成功
2025-08-01 16:21:19 | INFO     | personas.persona_manager:_load_personas:61 | 加载人设: 职场导师 (career_mentor)
2025-08-01 16:21:19 | INFO     | personas.persona_manager:_load_personas:61 | 加载人设: 推理专家 (reasoning_expert)
2025-08-01 16:21:19 | INFO     | personas.persona_manager:_load_personas:61 | 加载人设: 旅行向导 (travel_guide)
2025-08-01 16:21:19 | INFO     | personas.persona_manager:_load_personas:61 | 加载人设: 妻子模式 (wife)
2025-08-01 16:21:19 | INFO     | core.llm.model_manager:_load_config:56 | 配置文件加载成功
2025-08-01 16:21:19 | INFO     | core.llm.model_manager:_scan_models:72 | 发现模型: lucy_128k-Q8_0
2025-08-01 16:21:22 | INFO     | engine_2d.image_generator:__init__:79 | 2D Engine初始化完成，设备: cuda
2025-08-01 16:21:22 | INFO     | engine_2d.image_generator:__init__:80 | 发现 0 个可用模型
2025-08-01 16:21:22 | INFO     | personas.persona_manager:switch_persona:107 | 切换到人设: 职场导师
2025-08-01 16:21:22 | INFO     | memory.context_engine:start_session:252 | 开始新会话: test_persona_20250801_162122
2025-08-01 16:21:22 | INFO     | memory.context_engine:add_memory:188 | 添加记忆: 7e5ccd8f... (重要性: 0.70)
2025-08-01 16:21:22 | INFO     | memory.context_engine:add_memory:188 | 添加记忆: 200f88a2... (重要性: 0.50)
2025-08-01 16:21:31 | INFO     | core.utils.config:load_config:36 | 配置文件加载成功
2025-08-01 16:21:31 | INFO     | core.llm.model_manager:_load_config:56 | 配置文件加载成功
2025-08-01 16:21:31 | INFO     | core.llm.model_manager:_scan_models:72 | 发现模型: lucy_128k-Q8_0
2025-08-01 16:21:31 | INFO     | personas.persona_manager:_load_personas:61 | 加载人设: 职场导师 (career_mentor)
2025-08-01 16:21:31 | INFO     | personas.persona_manager:_load_personas:61 | 加载人设: 推理专家 (reasoning_expert)
2025-08-01 16:21:31 | INFO     | personas.persona_manager:_load_personas:61 | 加载人设: 旅行向导 (travel_guide)
2025-08-01 16:21:31 | INFO     | personas.persona_manager:_load_personas:61 | 加载人设: 妻子模式 (wife)
2025-08-01 16:21:35 | INFO     | engine_2d.image_generator:__init__:79 | 2D Engine初始化完成，设备: cuda
2025-08-01 16:21:35 | INFO     | engine_2d.image_generator:__init__:80 | 发现 0 个可用模型
2025-08-01 16:21:35 | INFO     | ui.app.main:__init__:45 | Reverie Agents 桌面应用启动成功
2025-08-01 16:21:35 | INFO     | core.utils.config:save_config:164 | 配置文件保存成功
2025-08-01 16:21:35 | INFO     | ui.app.main:closeEvent:260 | 应用程序关闭
2025-08-01 16:21:35 | INFO     | personas.persona_manager:switch_persona:107 | 切换到人设: 职场导师
2025-08-01 16:21:35 | INFO     | memory.context_engine:start_session:252 | 开始新会话: career_mentor_20250801_162135
2025-08-01 16:21:35 | INFO     | ui.web.app:start_new_session:51 | 开始新会话: career_mentor_20250801_162135
2025-08-01 16:21:35 | INFO     | personas.persona_manager:switch_persona:107 | 切换到人设: 妻子模式
2025-08-01 16:21:49 | INFO     | core.utils.config:load_config:36 | 配置文件加载成功
2025-08-01 16:21:49 | INFO     | core.llm.model_manager:_load_config:56 | 配置文件加载成功
2025-08-01 16:21:49 | INFO     | core.llm.model_manager:_scan_models:72 | 发现模型: lucy_128k-Q8_0
2025-08-01 16:21:49 | INFO     | personas.persona_manager:_load_personas:61 | 加载人设: 职场导师 (career_mentor)
2025-08-01 16:21:49 | INFO     | personas.persona_manager:_load_personas:61 | 加载人设: 推理专家 (reasoning_expert)
2025-08-01 16:21:49 | INFO     | personas.persona_manager:_load_personas:61 | 加载人设: 旅行向导 (travel_guide)
2025-08-01 16:21:49 | INFO     | personas.persona_manager:_load_personas:61 | 加载人设: 妻子模式 (wife)
2025-08-01 16:21:49 | INFO     | __main__:__init__:45 | Reverie Agents 桌面应用启动成功
2025-08-01 16:21:53 | INFO     | core.utils.config:save_config:164 | 配置文件保存成功
2025-08-01 16:21:53 | INFO     | __main__:closeEvent:260 | 应用程序关闭
2025-08-01 16:27:30 | INFO     | memory.history_manager:__init__:44 | 历史记录目录: D:\Reverie\Reverie Agents RIO\history
2025-08-01 16:27:30 | INFO     | core.utils.config:load_config:36 | 配置文件加载成功
2025-08-01 16:27:30 | INFO     | personas.persona_manager:_load_personas:61 | 加载人设: 职场导师 (career_mentor)
2025-08-01 16:27:30 | INFO     | personas.persona_manager:_load_personas:61 | 加载人设: 推理专家 (reasoning_expert)
2025-08-01 16:27:30 | INFO     | personas.persona_manager:_load_personas:61 | 加载人设: 旅行向导 (travel_guide)
2025-08-01 16:27:30 | INFO     | personas.persona_manager:_load_personas:61 | 加载人设: 妻子模式 (wife)
2025-08-01 16:27:30 | WARNING  | memory.history_manager:load_session:91 | 会话文件不存在: test_history_20250801_162730.json
2025-08-01 16:27:30 | INFO     | memory.history_manager:save_session:77 | 会话已保存: test_history_20250801_162730.json
2025-08-01 16:27:30 | INFO     | memory.history_manager:load_session:121 | 会话已加载: test_history_20250801_162730.json (1 条消息)
2025-08-01 16:27:30 | INFO     | memory.history_manager:save_session:77 | 会话已保存: test_history_20250801_162730.json
2025-08-01 16:27:30 | INFO     | memory.history_manager:load_session:121 | 会话已加载: test_history_20250801_162730.json (2 条消息)
2025-08-01 16:27:30 | INFO     | memory.history_manager:get_session_list:158 | 找到 1 个历史会话
2025-08-01 16:27:30 | INFO     | personas.persona_manager:switch_persona:107 | 切换到人设: 妻子模式
2025-08-01 16:27:30 | INFO     | memory.context_engine:start_session:264 | 开始新会话: wife_20250801_162730
2025-08-01 16:27:30 | WARNING  | memory.history_manager:load_session:91 | 会话文件不存在: wife_20250801_162730.json
2025-08-01 16:27:30 | INFO     | memory.history_manager:save_session:77 | 会话已保存: wife_20250801_162730.json
2025-08-01 16:27:30 | INFO     | memory.context_engine:add_memory:200 | 添加记忆: 8fd94c5e... (重要性: 0.80)
2025-08-01 16:27:30 | INFO     | memory.history_manager:load_session:121 | 会话已加载: wife_20250801_162730.json (1 条消息)
2025-08-01 16:27:30 | INFO     | memory.history_manager:save_session:77 | 会话已保存: wife_20250801_162730.json
2025-08-01 16:27:30 | INFO     | memory.context_engine:add_memory:200 | 添加记忆: ea50d8b2... (重要性: 0.50)
2025-08-01 16:27:30 | INFO     | memory.history_manager:get_session_list:158 | 找到 2 个历史会话
2025-08-01 16:27:30 | INFO     | memory.history_manager:load_session:121 | 会话已加载: wife_20250801_162730.json (2 条消息)
2025-08-01 16:27:30 | INFO     | memory.context_engine:load_session:358 | 会话加载成功: wife_20250801_162730
2025-08-01 16:27:57 | INFO     | core.utils.config:load_config:36 | 配置文件加载成功
2025-08-01 16:27:57 | INFO     | memory.history_manager:__init__:44 | 历史记录目录: D:\Reverie\Reverie Agents RIO\history
2025-08-01 16:27:57 | INFO     | core.llm.model_manager:_load_config:56 | 配置文件加载成功
2025-08-01 16:27:57 | INFO     | core.llm.model_manager:_scan_models:72 | 发现模型: lucy_128k-Q8_0
2025-08-01 16:27:57 | INFO     | personas.persona_manager:_load_personas:61 | 加载人设: 职场导师 (career_mentor)
2025-08-01 16:27:57 | INFO     | personas.persona_manager:_load_personas:61 | 加载人设: 推理专家 (reasoning_expert)
2025-08-01 16:27:57 | INFO     | personas.persona_manager:_load_personas:61 | 加载人设: 旅行向导 (travel_guide)
2025-08-01 16:27:57 | INFO     | personas.persona_manager:_load_personas:61 | 加载人设: 妻子模式 (wife)
2025-08-01 16:27:57 | INFO     | memory.history_manager:get_session_list:158 | 找到 2 个历史会话
2025-08-01 16:27:57 | INFO     | __main__:__init__:45 | Reverie Agents 桌面应用启动成功
2025-08-01 16:28:00 | INFO     | memory.history_manager:load_session:121 | 会话已加载: test_history_20250801_162730.json (2 条消息)
2025-08-01 16:28:00 | INFO     | memory.context_engine:load_session:358 | 会话加载成功: test_history_20250801_162730
2025-08-01 16:28:00 | INFO     | __main__:load_chat:170 | 加载对话: test_history_20250801_162730
2025-08-01 16:28:01 | INFO     | memory.history_manager:load_session:121 | 会话已加载: wife_20250801_162730.json (2 条消息)
2025-08-01 16:28:01 | INFO     | memory.context_engine:load_session:358 | 会话加载成功: wife_20250801_162730
2025-08-01 16:28:01 | INFO     | __main__:load_chat:170 | 加载对话: wife_20250801_162730
2025-08-01 16:28:01 | INFO     | memory.history_manager:load_session:121 | 会话已加载: test_history_20250801_162730.json (2 条消息)
2025-08-01 16:28:01 | INFO     | memory.context_engine:load_session:358 | 会话加载成功: test_history_20250801_162730
2025-08-01 16:28:01 | INFO     | __main__:load_chat:170 | 加载对话: test_history_20250801_162730
2025-08-01 16:28:03 | INFO     | core.utils.config:save_config:164 | 配置文件保存成功
2025-08-01 16:28:03 | INFO     | __main__:closeEvent:260 | 应用程序关闭
2025-08-01 16:29:14 | INFO     | core.utils.config:load_config:36 | 配置文件加载成功
2025-08-01 16:29:14 | INFO     | memory.history_manager:__init__:44 | 历史记录目录: D:\Reverie\Reverie Agents RIO\history
2025-08-01 16:29:14 | INFO     | personas.persona_manager:_load_personas:61 | 加载人设: 职场导师 (career_mentor)
2025-08-01 16:29:14 | INFO     | personas.persona_manager:_load_personas:61 | 加载人设: 推理专家 (reasoning_expert)
2025-08-01 16:29:14 | INFO     | personas.persona_manager:_load_personas:61 | 加载人设: 旅行向导 (travel_guide)
2025-08-01 16:29:14 | INFO     | personas.persona_manager:_load_personas:61 | 加载人设: 妻子模式 (wife)
2025-08-01 16:29:14 | INFO     | personas.persona_manager:switch_persona:107 | 切换到人设: 妻子模式
2025-08-01 16:29:14 | INFO     | memory.context_engine:start_session:264 | 开始新会话: wife_20250801_162914
2025-08-01 16:29:14 | WARNING  | memory.history_manager:load_session:91 | 会话文件不存在: wife_20250801_162914.json
2025-08-01 16:29:14 | INFO     | memory.history_manager:save_session:77 | 会话已保存: wife_20250801_162914.json
2025-08-01 16:29:14 | INFO     | memory.context_engine:add_memory:200 | 添加记忆: f44a13fb... (重要性: 0.70)
2025-08-01 16:29:14 | INFO     | memory.history_manager:load_session:121 | 会话已加载: wife_20250801_162914.json (1 条消息)
2025-08-01 16:29:14 | INFO     | memory.history_manager:save_session:77 | 会话已保存: wife_20250801_162914.json
2025-08-01 16:29:14 | INFO     | memory.context_engine:add_memory:200 | 添加记忆: eef2f701... (重要性: 0.60)
2025-08-01 16:29:14 | INFO     | memory.history_manager:load_session:121 | 会话已加载: wife_20250801_162914.json (2 条消息)
2025-08-01 16:29:14 | INFO     | memory.history_manager:save_session:77 | 会话已保存: wife_20250801_162914.json
2025-08-01 16:29:14 | INFO     | memory.context_engine:add_memory:200 | 添加记忆: 4449b072... (重要性: 0.70)
2025-08-01 16:29:14 | INFO     | memory.history_manager:load_session:121 | 会话已加载: wife_20250801_162914.json (3 条消息)
2025-08-01 16:29:14 | INFO     | memory.history_manager:save_session:77 | 会话已保存: wife_20250801_162914.json
2025-08-01 16:29:14 | INFO     | memory.context_engine:add_memory:200 | 添加记忆: c1569f91... (重要性: 0.50)
2025-08-01 16:29:14 | INFO     | memory.history_manager:load_session:121 | 会话已加载: wife_20250801_162914.json (4 条消息)
2025-08-01 16:29:14 | INFO     | memory.history_manager:save_session:77 | 会话已保存: wife_20250801_162914.json
2025-08-01 16:29:14 | INFO     | memory.context_engine:add_memory:200 | 添加记忆: d9509347... (重要性: 0.80)
2025-08-01 16:29:14 | INFO     | memory.history_manager:load_session:121 | 会话已加载: wife_20250801_162914.json (5 条消息)
2025-08-01 16:29:14 | INFO     | memory.history_manager:save_session:77 | 会话已保存: wife_20250801_162914.json
2025-08-01 16:29:14 | INFO     | memory.context_engine:add_memory:200 | 添加记忆: 9e2d5a0d... (重要性: 0.70)
2025-08-01 16:29:14 | INFO     | personas.persona_manager:switch_persona:107 | 切换到人设: 职场导师
2025-08-01 16:29:14 | INFO     | memory.context_engine:start_session:264 | 开始新会话: career_mentor_20250801_162914
2025-08-01 16:29:14 | WARNING  | memory.history_manager:load_session:91 | 会话文件不存在: career_mentor_20250801_162914.json
2025-08-01 16:29:14 | INFO     | memory.history_manager:save_session:77 | 会话已保存: career_mentor_20250801_162914.json
2025-08-01 16:29:14 | INFO     | memory.context_engine:add_memory:200 | 添加记忆: 8aea82e5... (重要性: 0.80)
2025-08-01 16:29:15 | INFO     | memory.history_manager:load_session:121 | 会话已加载: career_mentor_20250801_162914.json (1 条消息)
2025-08-01 16:29:15 | INFO     | memory.history_manager:save_session:77 | 会话已保存: career_mentor_20250801_162914.json
2025-08-01 16:29:15 | INFO     | memory.context_engine:add_memory:200 | 添加记忆: e0a78c71... (重要性: 0.50)
2025-08-01 16:29:15 | INFO     | memory.history_manager:load_session:121 | 会话已加载: career_mentor_20250801_162914.json (2 条消息)
2025-08-01 16:29:15 | INFO     | memory.history_manager:save_session:77 | 会话已保存: career_mentor_20250801_162914.json
2025-08-01 16:29:15 | INFO     | memory.context_engine:add_memory:200 | 添加记忆: 46c40b8f... (重要性: 0.70)
2025-08-01 16:29:15 | INFO     | memory.history_manager:load_session:121 | 会话已加载: career_mentor_20250801_162914.json (3 条消息)
2025-08-01 16:29:15 | INFO     | memory.history_manager:save_session:77 | 会话已保存: career_mentor_20250801_162914.json
2025-08-01 16:29:15 | INFO     | memory.context_engine:add_memory:200 | 添加记忆: 20e0bcd1... (重要性: 0.50)
2025-08-01 16:29:15 | INFO     | memory.history_manager:load_session:121 | 会话已加载: career_mentor_20250801_162914.json (4 条消息)
2025-08-01 16:29:15 | INFO     | memory.history_manager:save_session:77 | 会话已保存: career_mentor_20250801_162914.json
2025-08-01 16:29:15 | INFO     | memory.context_engine:add_memory:200 | 添加记忆: b580ee42... (重要性: 0.80)
2025-08-01 16:29:15 | INFO     | memory.history_manager:load_session:121 | 会话已加载: career_mentor_20250801_162914.json (5 条消息)
2025-08-01 16:29:15 | INFO     | memory.history_manager:save_session:77 | 会话已保存: career_mentor_20250801_162914.json
2025-08-01 16:29:15 | INFO     | memory.context_engine:add_memory:200 | 添加记忆: a8a36c20... (重要性: 0.80)
2025-08-01 16:29:15 | INFO     | personas.persona_manager:switch_persona:107 | 切换到人设: 推理专家
2025-08-01 16:29:15 | INFO     | memory.context_engine:start_session:264 | 开始新会话: reasoning_expert_20250801_162915
2025-08-01 16:29:15 | WARNING  | memory.history_manager:load_session:91 | 会话文件不存在: reasoning_expert_20250801_162915.json
2025-08-01 16:29:15 | INFO     | memory.history_manager:save_session:77 | 会话已保存: reasoning_expert_20250801_162915.json
2025-08-01 16:29:15 | INFO     | memory.context_engine:add_memory:200 | 添加记忆: 1d77be5b... (重要性: 0.80)
2025-08-01 16:29:15 | INFO     | memory.history_manager:load_session:121 | 会话已加载: reasoning_expert_20250801_162915.json (1 条消息)
2025-08-01 16:29:15 | INFO     | memory.history_manager:save_session:77 | 会话已保存: reasoning_expert_20250801_162915.json
2025-08-01 16:29:15 | INFO     | memory.context_engine:add_memory:200 | 添加记忆: af7e8218... (重要性: 0.50)
2025-08-01 16:29:15 | INFO     | memory.history_manager:load_session:121 | 会话已加载: reasoning_expert_20250801_162915.json (2 条消息)
2025-08-01 16:29:15 | INFO     | memory.history_manager:save_session:77 | 会话已保存: reasoning_expert_20250801_162915.json
2025-08-01 16:29:15 | INFO     | memory.context_engine:add_memory:200 | 添加记忆: 88f58e97... (重要性: 0.80)
2025-08-01 16:29:16 | INFO     | memory.history_manager:load_session:121 | 会话已加载: reasoning_expert_20250801_162915.json (3 条消息)
2025-08-01 16:29:16 | INFO     | memory.history_manager:save_session:77 | 会话已保存: reasoning_expert_20250801_162915.json
2025-08-01 16:29:16 | INFO     | memory.context_engine:add_memory:200 | 添加记忆: b4bea944... (重要性: 0.80)
2025-08-01 16:29:16 | INFO     | memory.history_manager:load_session:121 | 会话已加载: reasoning_expert_20250801_162915.json (4 条消息)
2025-08-01 16:29:16 | INFO     | memory.history_manager:save_session:77 | 会话已保存: reasoning_expert_20250801_162915.json
2025-08-01 16:29:16 | INFO     | memory.context_engine:add_memory:200 | 添加记忆: 41310d49... (重要性: 0.80)
2025-08-01 16:29:16 | INFO     | memory.history_manager:load_session:121 | 会话已加载: reasoning_expert_20250801_162915.json (5 条消息)
2025-08-01 16:29:16 | INFO     | memory.history_manager:save_session:77 | 会话已保存: reasoning_expert_20250801_162915.json
2025-08-01 16:29:16 | INFO     | memory.context_engine:add_memory:200 | 添加记忆: a4efaecf... (重要性: 0.80)
2025-08-01 16:29:16 | INFO     | memory.history_manager:get_session_list:158 | 找到 5 个历史会话
2025-08-01 16:29:16 | INFO     | memory.history_manager:load_session:121 | 会话已加载: reasoning_expert_20250801_162915.json (6 条消息)
2025-08-01 16:29:16 | INFO     | memory.context_engine:load_session:358 | 会话加载成功: reasoning_expert_20250801_162915
2025-08-01 16:29:16 | INFO     | memory.history_manager:load_session:121 | 会话已加载: career_mentor_20250801_162914.json (6 条消息)
2025-08-01 16:29:16 | INFO     | memory.context_engine:load_session:358 | 会话加载成功: career_mentor_20250801_162914
2025-08-01 16:29:16 | INFO     | memory.history_manager:load_session:121 | 会话已加载: wife_20250801_162914.json (6 条消息)
2025-08-01 16:29:16 | INFO     | memory.context_engine:load_session:358 | 会话加载成功: wife_20250801_162914
2025-08-01 16:30:28 | INFO     | core.utils.config:load_config:36 | 配置文件加载成功
2025-08-01 16:30:28 | INFO     | memory.history_manager:__init__:44 | 历史记录目录: D:\Reverie\Reverie Agents RIO\history
2025-08-01 16:30:28 | INFO     | core.llm.model_manager:_load_config:56 | 配置文件加载成功
2025-08-01 16:30:28 | INFO     | core.llm.model_manager:_scan_models:72 | 发现模型: lucy_128k-Q8_0
2025-08-01 16:30:28 | INFO     | personas.persona_manager:_load_personas:61 | 加载人设: 职场导师 (career_mentor)
2025-08-01 16:30:28 | INFO     | personas.persona_manager:_load_personas:61 | 加载人设: 推理专家 (reasoning_expert)
2025-08-01 16:30:28 | INFO     | personas.persona_manager:_load_personas:61 | 加载人设: 旅行向导 (travel_guide)
2025-08-01 16:30:28 | INFO     | personas.persona_manager:_load_personas:61 | 加载人设: 妻子模式 (wife)
2025-08-01 16:30:28 | INFO     | memory.history_manager:get_session_list:158 | 找到 5 个历史会话
2025-08-01 16:30:28 | INFO     | __main__:__init__:45 | Reverie Agents 桌面应用启动成功
2025-08-01 16:30:35 | INFO     | memory.history_manager:load_session:121 | 会话已加载: test_history_20250801_162730.json (2 条消息)
2025-08-01 16:30:35 | INFO     | memory.context_engine:load_session:358 | 会话加载成功: test_history_20250801_162730
2025-08-01 16:30:35 | INFO     | __main__:load_chat:170 | 加载对话: test_history_20250801_162730
2025-08-01 16:30:36 | INFO     | memory.history_manager:load_session:121 | 会话已加载: wife_20250801_162730.json (2 条消息)
2025-08-01 16:30:36 | INFO     | memory.context_engine:load_session:358 | 会话加载成功: wife_20250801_162730
2025-08-01 16:30:36 | INFO     | __main__:load_chat:170 | 加载对话: wife_20250801_162730
2025-08-01 16:30:37 | INFO     | memory.history_manager:load_session:121 | 会话已加载: career_mentor_20250801_162914.json (6 条消息)
2025-08-01 16:30:37 | INFO     | memory.context_engine:load_session:358 | 会话加载成功: career_mentor_20250801_162914
2025-08-01 16:30:37 | INFO     | __main__:load_chat:170 | 加载对话: career_mentor_20250801_162914
2025-08-01 16:30:37 | INFO     | memory.history_manager:load_session:121 | 会话已加载: reasoning_expert_20250801_162915.json (6 条消息)
2025-08-01 16:30:37 | INFO     | memory.context_engine:load_session:358 | 会话加载成功: reasoning_expert_20250801_162915
2025-08-01 16:30:37 | INFO     | __main__:load_chat:170 | 加载对话: reasoning_expert_20250801_162915
2025-08-01 16:30:39 | INFO     | core.utils.config:save_config:164 | 配置文件保存成功
2025-08-01 16:30:39 | INFO     | __main__:closeEvent:260 | 应用程序关闭
2025-08-01 16:42:46 | INFO     | core.utils.config:load_config:36 | 配置文件加载成功
2025-08-01 16:42:46 | INFO     | memory.history_manager:__init__:44 | 历史记录目录: D:\Reverie\Reverie Agents RIO\history
2025-08-01 16:42:46 | INFO     | core.llm.model_manager:_load_config:56 | 配置文件加载成功
2025-08-01 16:42:46 | INFO     | core.llm.model_manager:_scan_models:72 | 发现模型: lucy_128k-Q8_0
2025-08-01 16:42:46 | INFO     | personas.persona_manager:_load_personas:61 | 加载人设: 职场导师 (career_mentor)
2025-08-01 16:42:46 | INFO     | personas.persona_manager:_load_personas:61 | 加载人设: 推理专家 (reasoning_expert)
2025-08-01 16:42:46 | INFO     | personas.persona_manager:_load_personas:61 | 加载人设: 旅行向导 (travel_guide)
2025-08-01 16:42:46 | INFO     | personas.persona_manager:_load_personas:61 | 加载人设: 妻子模式 (wife)
2025-08-01 16:42:46 | INFO     | memory.history_manager:get_session_list:158 | 找到 2 个历史会话
2025-08-01 16:42:46 | INFO     | __main__:__init__:45 | Reverie Agents 桌面应用启动成功
2025-08-01 16:42:57 | INFO     | core.utils.config:save_config:164 | 配置文件保存成功
2025-08-01 16:42:57 | INFO     | __main__:closeEvent:260 | 应用程序关闭
2025-08-01 17:19:44 | INFO     | core.utils.config:load_config:36 | 配置文件加载成功
2025-08-01 17:19:44 | INFO     | memory.history_manager:__init__:44 | 历史记录目录: D:\Reverie\Reverie Agents RIO\history
2025-08-01 17:19:45 | INFO     | core.llm.model_manager:_load_config:56 | 配置文件加载成功
2025-08-01 17:19:45 | INFO     | core.llm.model_manager:_scan_models:72 | 发现模型: lucy_128k-Q8_0
2025-08-01 17:19:45 | INFO     | personas.persona_manager:_load_personas:61 | 加载人设: 职场导师 (career_mentor)
2025-08-01 17:19:45 | INFO     | personas.persona_manager:_load_personas:61 | 加载人设: 推理专家 (reasoning_expert)
2025-08-01 17:19:45 | INFO     | personas.persona_manager:_load_personas:61 | 加载人设: 旅行向导 (travel_guide)
2025-08-01 17:19:45 | INFO     | personas.persona_manager:_load_personas:61 | 加载人设: 妻子模式 (wife)
2025-08-01 17:19:45 | INFO     | memory.history_manager:get_session_list:158 | 找到 2 个历史会话
2025-08-01 17:19:45 | INFO     | __main__:__init__:45 | Reverie Agents 桌面应用启动成功
2025-08-01 17:19:55 | INFO     | personas.persona_manager:switch_persona:107 | 切换到人设: 妻子模式
2025-08-01 17:19:55 | INFO     | __main__:change_persona:182 | 切换人设: 妻子模式
2025-08-01 17:20:04 | INFO     | core.llm.model_manager:load_model:112 | 正在加载模型: lucy_128k-Q8_0
2025-08-01 17:20:04 | ERROR    | core.llm.model_manager:load_model:126 | 模型加载失败: exception: access violation reading 0x0000000000000000
2025-08-01 17:28:04 | INFO     | core.utils.config:load_config:36 | 配置文件加载成功
2025-08-01 17:28:04 | INFO     | memory.history_manager:__init__:44 | 历史记录目录: D:\Reverie\Reverie Agents RIO\history
2025-08-01 17:28:04 | INFO     | core.llm.model_manager:_load_config:56 | 配置文件加载成功
2025-08-01 17:28:04 | INFO     | core.llm.model_manager:_scan_models:72 | 发现模型: lucy_128k-Q8_0
2025-08-01 17:28:04 | INFO     | personas.persona_manager:_load_personas:61 | 加载人设: 职场导师 (career_mentor)
2025-08-01 17:28:04 | INFO     | personas.persona_manager:_load_personas:61 | 加载人设: 推理专家 (reasoning_expert)
2025-08-01 17:28:04 | INFO     | personas.persona_manager:_load_personas:61 | 加载人设: 旅行向导 (travel_guide)
2025-08-01 17:28:04 | INFO     | personas.persona_manager:_load_personas:61 | 加载人设: 妻子模式 (wife)
2025-08-01 17:28:04 | INFO     | memory.history_manager:get_session_list:158 | 找到 2 个历史会话
2025-08-01 17:28:04 | INFO     | __main__:__init__:46 | Reverie Agents 桌面应用启动成功
2025-08-01 17:28:08 | INFO     | __main__:show_main_window:323 | 🎉 Reverie Agents 启动完成！
2025-08-01 17:28:57 | INFO     | core.llm.model_manager:_load_config:56 | 配置文件加载成功
2025-08-01 17:28:57 | INFO     | core.llm.model_manager:_scan_models:72 | 发现模型: lucy_128k-Q8_0
2025-08-01 17:28:57 | INFO     | core.utils.config:load_config:36 | 配置文件加载成功
2025-08-01 17:29:17 | INFO     | core.llm.model_manager:_load_config:56 | 配置文件加载成功
2025-08-01 17:29:17 | INFO     | core.llm.model_manager:_scan_models:72 | 发现模型: lucy_128k-Q8_0
2025-08-01 17:29:17 | INFO     | core.utils.config:load_config:36 | 配置文件加载成功
2025-08-01 17:29:17 | INFO     | core.llm.model_manager:_scan_models:72 | 发现模型: lucy_128k-Q8_0
2025-08-01 17:29:17 | INFO     | core.llm.model_manager:load_model:130 | 正在加载模型: lucy_128k-Q8_0
2025-08-01 17:29:17 | INFO     | core.llm.model_manager:load_model:131 | 模型路径: models/llm\lucy_128k-Q8_0.gguf
2025-08-01 17:29:17 | INFO     | core.llm.model_manager:load_model:132 | 上下文长度: 0
2025-08-01 17:29:17 | INFO     | core.llm.model_manager:load_model:133 | GPU层数: -1
2025-08-01 17:29:17 | INFO     | core.llm.model_manager:load_model:134 | 线程数: 4
2025-08-01 17:29:17 | INFO     | core.llm.model_manager:load_model:154 | 模型加载成功: lucy_128k-Q8_0
2025-08-01 17:30:22 | INFO     | personas.persona_manager:switch_persona:107 | 切换到人设: 妻子模式
2025-08-01 17:30:22 | INFO     | __main__:change_persona:183 | 切换人设: 妻子模式
2025-08-01 17:30:25 | INFO     | core.llm.model_manager:load_model:130 | 正在加载模型: lucy_128k-Q8_0
2025-08-01 17:30:25 | INFO     | core.llm.model_manager:load_model:131 | 模型路径: models/llm\lucy_128k-Q8_0.gguf
2025-08-01 17:30:25 | INFO     | core.llm.model_manager:load_model:132 | 上下文长度: 0
2025-08-01 17:30:25 | INFO     | core.llm.model_manager:load_model:133 | GPU层数: -1
2025-08-01 17:30:25 | INFO     | core.llm.model_manager:load_model:134 | 线程数: 4
2025-08-01 17:30:25 | ERROR    | core.llm.model_manager:load_model:158 | 模型加载失败: exception: access violation reading 0x0000000000000000
2025-08-01 17:30:25 | ERROR    | core.llm.model_manager:load_model:159 | 错误类型: OSError
2025-08-01 17:30:29 | INFO     | core.utils.config:save_config:164 | 配置文件保存成功
2025-08-01 17:30:29 | INFO     | __main__:closeEvent:261 | 应用程序关闭
2025-08-01 17:36:10 | INFO     | core.utils.config:load_config:36 | 配置文件加载成功
2025-08-01 17:36:10 | INFO     | memory.history_manager:__init__:44 | 历史记录目录: D:\Reverie\Reverie Agents RIO\history
2025-08-01 17:36:10 | INFO     | core.llm.model_manager:_load_config:56 | 配置文件加载成功
2025-08-01 17:36:10 | INFO     | core.llm.model_manager:_scan_models:72 | 发现模型: lucy_128k-Q8_0
2025-08-01 17:36:10 | INFO     | personas.persona_manager:_load_personas:61 | 加载人设: 职场导师 (career_mentor)
2025-08-01 17:36:10 | INFO     | personas.persona_manager:_load_personas:61 | 加载人设: 推理专家 (reasoning_expert)
2025-08-01 17:36:10 | INFO     | personas.persona_manager:_load_personas:61 | 加载人设: 旅行向导 (travel_guide)
2025-08-01 17:36:10 | INFO     | personas.persona_manager:_load_personas:61 | 加载人设: 妻子模式 (wife)
2025-08-01 17:36:10 | INFO     | memory.history_manager:get_session_list:158 | 找到 2 个历史会话
2025-08-01 17:36:10 | INFO     | __main__:__init__:46 | Reverie Agents 桌面应用启动成功
2025-08-01 17:36:14 | INFO     | __main__:show_main_window:323 | 🎉 Reverie Agents 启动完成！
2025-08-01 17:36:15 | INFO     | memory.history_manager:load_session:121 | 会话已加载: wife_20250801_162914.json (6 条消息)
2025-08-01 17:36:15 | INFO     | memory.context_engine:load_session:358 | 会话加载成功: wife_20250801_162914
2025-08-01 17:36:15 | INFO     | __main__:load_chat:171 | 加载对话: wife_20250801_162914
2025-08-01 17:36:15 | INFO     | memory.history_manager:load_session:121 | 会话已加载: career_mentor_20250801_162914.json (6 条消息)
2025-08-01 17:36:15 | INFO     | memory.context_engine:load_session:358 | 会话加载成功: career_mentor_20250801_162914
2025-08-01 17:36:15 | INFO     | __main__:load_chat:171 | 加载对话: career_mentor_20250801_162914
2025-08-01 17:36:50 | INFO     | personas.persona_manager:switch_persona:107 | 切换到人设: 妻子模式
2025-08-01 17:36:50 | INFO     | memory.context_engine:start_session:264 | 开始新会话: wife_20250801_173650
2025-08-01 17:36:50 | INFO     | __main__:start_new_chat:163 | 开始新对话: wife_20250801_173650
2025-08-01 17:36:50 | INFO     | __main__:change_persona:183 | 切换人设: 妻子模式
2025-08-01 17:36:53 | INFO     | core.llm.model_manager:load_model:130 | 正在加载模型: lucy_128k-Q8_0
2025-08-01 17:36:53 | INFO     | core.llm.model_manager:load_model:131 | 模型路径: models/llm\lucy_128k-Q8_0.gguf
2025-08-01 17:36:53 | INFO     | core.llm.model_manager:load_model:132 | 上下文长度: 0
2025-08-01 17:36:53 | INFO     | core.llm.model_manager:load_model:133 | GPU层数: -1
2025-08-01 17:36:53 | INFO     | core.llm.model_manager:load_model:134 | 线程数: 4
2025-08-01 17:36:53 | ERROR    | core.llm.model_manager:load_model:158 | 模型加载失败: exception: access violation reading 0x0000000000000000
2025-08-01 17:36:53 | ERROR    | core.llm.model_manager:load_model:159 | 错误类型: OSError
2025-08-01 17:47:19 | INFO     | core.utils.config:load_config:36 | 配置文件加载成功
2025-08-01 17:47:19 | INFO     | memory.history_manager:__init__:44 | 历史记录目录: D:\Reverie\Reverie Agents RIO\history
2025-08-01 17:47:19 | INFO     | core.llm.model_manager:_load_config:56 | 配置文件加载成功
2025-08-01 17:47:19 | INFO     | core.llm.model_manager:_scan_models:72 | 发现模型: lucy_128k-Q8_0
2025-08-01 17:47:19 | INFO     | personas.persona_manager:_load_personas:61 | 加载人设: 职场导师 (career_mentor)
2025-08-01 17:47:19 | INFO     | personas.persona_manager:_load_personas:61 | 加载人设: 推理专家 (reasoning_expert)
2025-08-01 17:47:19 | INFO     | personas.persona_manager:_load_personas:61 | 加载人设: 旅行向导 (travel_guide)
2025-08-01 17:47:19 | INFO     | personas.persona_manager:_load_personas:61 | 加载人设: 妻子模式 (wife)
2025-08-01 17:47:19 | INFO     | memory.history_manager:get_session_list:158 | 找到 2 个历史会话
2025-08-01 17:47:19 | INFO     | __main__:__init__:48 | Reverie Agents 桌面应用启动成功
2025-08-01 17:47:23 | INFO     | __main__:show_main_window:337 | 🎉 Reverie Agents 启动完成！
2025-08-01 17:49:27 | INFO     | core.llm.model_manager:load_model:130 | 正在加载模型: lucy_128k-Q8_0
2025-08-01 17:49:27 | INFO     | core.llm.model_manager:load_model:131 | 模型路径: models/llm\lucy_128k-Q8_0.gguf
2025-08-01 17:49:27 | INFO     | core.llm.model_manager:load_model:132 | 上下文长度: 0
2025-08-01 17:49:27 | INFO     | core.llm.model_manager:load_model:133 | GPU层数: -1
2025-08-01 17:49:27 | INFO     | core.llm.model_manager:load_model:134 | 线程数: 4
2025-08-01 17:49:27 | ERROR    | core.llm.model_manager:load_model:158 | 模型加载失败: exception: access violation reading 0x0000000000000000
2025-08-01 17:49:27 | ERROR    | core.llm.model_manager:load_model:159 | 错误类型: OSError
2025-08-01 17:49:30 | INFO     | core.utils.config:save_config:164 | 配置文件保存成功
2025-08-01 17:49:30 | INFO     | __main__:closeEvent:275 | 应用程序关闭
2025-08-01 18:01:23 | INFO     | core.utils.config:load_config:36 | 配置文件加载成功
2025-08-01 18:01:23 | INFO     | core.llm.model_manager:_load_config:56 | 配置文件加载成功
2025-08-01 18:01:23 | INFO     | core.llm.model_manager:_scan_models:72 | 发现模型: lucy_128k-Q8_0
2025-08-01 18:01:25 | INFO     | engine_2d.image_generator:__init__:115 | 2D Engine初始化完成，设备: cuda
2025-08-01 18:01:25 | INFO     | engine_2d.image_generator:__init__:116 | 发现 0 个可用模型
2025-08-01 18:01:25 | INFO     | engine_2d.image_generator:__init__:117 | 支持 14 种采样器
2025-08-01 18:01:25 | INFO     | engine_2d.image_generator:_detect_model_type_from_name:186 | 检测到模型类型: flux (文件: flux-dev-Q4_K_S.gguf)
2025-08-01 18:01:25 | INFO     | engine_2d.image_generator:_detect_model_type_from_name:186 | 检测到模型类型: sdxl (文件: stable-diffusion-xl-base-1.0.safetensors)
2025-08-01 18:01:25 | INFO     | engine_2d.image_generator:_detect_model_type_from_name:186 | 检测到模型类型: illustrious (文件: illustrious-xl-v0.1.safetensors)
2025-08-01 18:01:25 | INFO     | engine_2d.image_generator:_detect_model_type_from_name:186 | 检测到模型类型: sd2 (文件: stable-diffusion-2-1.safetensors)
2025-08-01 18:01:25 | INFO     | engine_2d.image_generator:_detect_model_type_from_name:186 | 检测到模型类型: kandinsky (文件: kandinsky-2-2.safetensors)
2025-08-01 18:01:25 | INFO     | engine_2d.image_generator:_detect_model_type_from_name:190 | 使用默认模型类型: sd (文件: some-random-model.safetensors)
2025-08-01 18:01:25 | INFO     | core.llm.model_manager:load_model:130 | 正在加载模型: lucy_128k-Q8_0
2025-08-01 18:01:25 | INFO     | core.llm.model_manager:load_model:131 | 模型路径: models/llm\lucy_128k-Q8_0.gguf
2025-08-01 18:01:25 | INFO     | core.llm.model_manager:load_model:132 | 上下文长度: 131072
2025-08-01 18:01:25 | INFO     | core.llm.model_manager:load_model:133 | GPU层数: -1
2025-08-01 18:01:25 | INFO     | core.llm.model_manager:load_model:134 | 线程数: 4
2025-08-01 18:01:28 | INFO     | core.llm.model_manager:load_model:184 | 模型加载成功: lucy_128k-Q8_0
2025-08-01 18:01:56 | INFO     | core.utils.config:load_config:36 | 配置文件加载成功
2025-08-01 18:01:56 | INFO     | core.llm.model_manager:_load_config:56 | 配置文件加载成功
2025-08-01 18:01:56 | INFO     | core.llm.model_manager:_scan_models:72 | 发现模型: lucy_128k-Q8_0
2025-08-01 18:01:59 | INFO     | engine_2d.image_generator:__init__:115 | 2D Engine初始化完成，设备: cuda
2025-08-01 18:01:59 | INFO     | engine_2d.image_generator:__init__:116 | 发现 0 个可用模型
2025-08-01 18:01:59 | INFO     | engine_2d.image_generator:__init__:117 | 支持 14 种采样器
2025-08-01 18:01:59 | INFO     | engine_2d.image_generator:_detect_model_type_from_name:186 | 检测到模型类型: flux (文件: flux-dev-Q4_K_S.gguf)
2025-08-01 18:01:59 | INFO     | engine_2d.image_generator:_detect_model_type_from_name:186 | 检测到模型类型: sdxl (文件: stable-diffusion-xl-base-1.0.safetensors)
2025-08-01 18:01:59 | INFO     | engine_2d.image_generator:_detect_model_type_from_name:186 | 检测到模型类型: illustrious (文件: illustrious-xl-v0.1.safetensors)
2025-08-01 18:01:59 | INFO     | engine_2d.image_generator:_detect_model_type_from_name:186 | 检测到模型类型: sd2 (文件: stable-diffusion-2-1.safetensors)
2025-08-01 18:01:59 | INFO     | engine_2d.image_generator:_detect_model_type_from_name:186 | 检测到模型类型: kandinsky (文件: kandinsky-2-2.safetensors)
2025-08-01 18:01:59 | INFO     | engine_2d.image_generator:_detect_model_type_from_name:190 | 使用默认模型类型: sd (文件: some-random-model.safetensors)
2025-08-01 18:01:59 | INFO     | core.llm.model_manager:load_model:130 | 正在加载模型: lucy_128k-Q8_0
2025-08-01 18:01:59 | INFO     | core.llm.model_manager:load_model:131 | 模型路径: models/llm\lucy_128k-Q8_0.gguf
2025-08-01 18:01:59 | INFO     | core.llm.model_manager:load_model:132 | 上下文长度: 131072
2025-08-01 18:01:59 | INFO     | core.llm.model_manager:load_model:133 | GPU层数: -1
2025-08-01 18:01:59 | INFO     | core.llm.model_manager:load_model:134 | 线程数: 4
2025-08-01 18:02:01 | INFO     | core.llm.model_manager:load_model:184 | 模型加载成功: lucy_128k-Q8_0
2025-08-01 18:02:39 | INFO     | core.utils.config:load_config:36 | 配置文件加载成功
2025-08-01 18:02:39 | INFO     | core.llm.model_manager:_load_config:56 | 配置文件加载成功
2025-08-01 18:02:39 | INFO     | core.llm.model_manager:_scan_models:72 | 发现模型: lucy_128k-Q8_0
2025-08-01 18:02:41 | INFO     | engine_2d.image_generator:__init__:115 | 2D Engine初始化完成，设备: cuda
2025-08-01 18:02:41 | INFO     | engine_2d.image_generator:__init__:116 | 发现 0 个可用模型
2025-08-01 18:02:41 | INFO     | engine_2d.image_generator:__init__:117 | 支持 14 种采样器
2025-08-01 18:02:41 | INFO     | engine_2d.image_generator:_detect_model_type_from_name:186 | 检测到模型类型: flux (文件: flux-dev-Q4_K_S.gguf)
2025-08-01 18:02:41 | INFO     | engine_2d.image_generator:_detect_model_type_from_name:186 | 检测到模型类型: sdxl (文件: stable-diffusion-xl-base-1.0.safetensors)
2025-08-01 18:02:41 | INFO     | engine_2d.image_generator:_detect_model_type_from_name:186 | 检测到模型类型: illustrious (文件: illustrious-xl-v0.1.safetensors)
2025-08-01 18:02:41 | INFO     | engine_2d.image_generator:_detect_model_type_from_name:186 | 检测到模型类型: sd2 (文件: stable-diffusion-2-1.safetensors)
2025-08-01 18:02:41 | INFO     | engine_2d.image_generator:_detect_model_type_from_name:186 | 检测到模型类型: kandinsky (文件: kandinsky-2-2.safetensors)
2025-08-01 18:02:41 | INFO     | engine_2d.image_generator:_detect_model_type_from_name:190 | 使用默认模型类型: sd (文件: some-random-model.safetensors)
2025-08-01 18:02:41 | INFO     | core.llm.model_manager:load_model:130 | 正在加载模型: lucy_128k-Q8_0
2025-08-01 18:02:41 | INFO     | core.llm.model_manager:load_model:131 | 模型路径: models/llm\lucy_128k-Q8_0.gguf
2025-08-01 18:02:41 | INFO     | core.llm.model_manager:load_model:132 | 上下文长度: 131072
2025-08-01 18:02:41 | INFO     | core.llm.model_manager:load_model:133 | GPU层数: -1
2025-08-01 18:02:41 | INFO     | core.llm.model_manager:load_model:134 | 线程数: 4
2025-08-01 18:02:44 | INFO     | core.llm.model_manager:load_model:184 | 模型加载成功: lucy_128k-Q8_0
2025-08-01 18:09:25 | INFO     | core.utils.config:load_config:36 | 配置文件加载成功
2025-08-01 18:09:25 | INFO     | memory.history_manager:__init__:44 | 历史记录目录: D:\Reverie\Reverie Agents RIO\history
2025-08-01 18:09:25 | INFO     | core.llm.model_manager:_load_config:56 | 配置文件加载成功
2025-08-01 18:09:25 | INFO     | core.llm.model_manager:_scan_models:72 | 发现模型: lucy_128k-Q8_0
2025-08-01 18:09:28 | INFO     | engine_2d.image_generator:__init__:115 | 2D Engine初始化完成，设备: cuda
2025-08-01 18:09:28 | INFO     | engine_2d.image_generator:__init__:116 | 发现 0 个可用模型
2025-08-01 18:09:28 | INFO     | engine_2d.image_generator:__init__:117 | 支持 14 种采样器
2025-08-01 18:09:28 | INFO     | personas.persona_manager:_load_personas:61 | 加载人设: 职场导师 (career_mentor)
2025-08-01 18:09:28 | INFO     | personas.persona_manager:_load_personas:61 | 加载人设: 推理专家 (reasoning_expert)
2025-08-01 18:09:28 | INFO     | personas.persona_manager:_load_personas:61 | 加载人设: 旅行向导 (travel_guide)
2025-08-01 18:09:28 | INFO     | personas.persona_manager:_load_personas:61 | 加载人设: 妻子模式 (wife)
2025-08-01 18:09:28 | INFO     | memory.history_manager:get_session_list:158 | 找到 2 个历史会话
2025-08-01 18:09:28 | INFO     | __main__:__init__:48 | Reverie Agents 桌面应用启动成功
2025-08-01 18:09:32 | INFO     | __main__:show_main_window:337 | 🎉 Reverie Agents 启动完成！
2025-08-01 18:10:00 | INFO     | core.utils.config:save_config:164 | 配置文件保存成功
2025-08-01 18:10:02 | INFO     | core.utils.config:save_config:164 | 配置文件保存成功
2025-08-01 18:10:07 | INFO     | personas.persona_manager:switch_persona:107 | 切换到人设: 妻子模式
2025-08-01 18:10:07 | INFO     | __main__:change_persona:191 | 切换人设: 妻子模式
2025-08-01 18:10:12 | INFO     | core.llm.model_manager:load_model:130 | 正在加载模型: lucy_128k-Q8_0
2025-08-01 18:10:12 | INFO     | core.llm.model_manager:load_model:131 | 模型路径: models/llm\lucy_128k-Q8_0.gguf
2025-08-01 18:10:12 | INFO     | core.llm.model_manager:load_model:132 | 上下文长度: 131072
2025-08-01 18:10:12 | INFO     | core.llm.model_manager:load_model:133 | GPU层数: -1
2025-08-01 18:10:12 | INFO     | core.llm.model_manager:load_model:134 | 线程数: 4
2025-08-01 18:10:12 | WARNING  | core.llm.model_manager:load_model:178 | CUDA加载失败，尝试CPU模式: exception: access violation reading 0x0000000000000000
2025-08-01 18:10:12 | ERROR    | core.llm.model_manager:load_model:188 | 模型加载失败: exception: access violation reading 0x0000000000000000
2025-08-01 18:10:12 | ERROR    | core.llm.model_manager:load_model:189 | 错误类型: OSError
2025-08-01 18:11:01 | INFO     | core.utils.config:save_config:164 | 配置文件保存成功
2025-08-01 18:11:01 | INFO     | __main__:closeEvent:275 | 应用程序关闭
2025-08-01 18:35:20 | INFO     | core.utils.config:load_config:36 | 配置文件加载成功
2025-08-01 18:35:20 | INFO     | memory.history_manager:__init__:44 | 历史记录目录: D:\Reverie\Reverie Agents RIO\history
2025-08-01 18:35:20 | INFO     | core.llm.model_manager:_load_config:56 | 配置文件加载成功
2025-08-01 18:35:20 | INFO     | core.llm.model_manager:_scan_models:72 | 发现模型: lucy_128k-Q8_0
2025-08-01 18:35:23 | INFO     | engine_2d.image_generator:__init__:115 | 2D Engine初始化完成，设备: cuda
2025-08-01 18:35:23 | INFO     | engine_2d.image_generator:__init__:116 | 发现 0 个可用模型
2025-08-01 18:35:23 | INFO     | engine_2d.image_generator:__init__:117 | 支持 14 种采样器
2025-08-01 18:35:23 | INFO     | personas.persona_manager:_load_personas:61 | 加载人设: 职场导师 (career_mentor)
2025-08-01 18:35:23 | INFO     | personas.persona_manager:_load_personas:61 | 加载人设: 推理专家 (reasoning_expert)
2025-08-01 18:35:23 | INFO     | personas.persona_manager:_load_personas:61 | 加载人设: 旅行向导 (travel_guide)
2025-08-01 18:35:23 | INFO     | personas.persona_manager:_load_personas:61 | 加载人设: 妻子模式 (wife)
2025-08-01 18:35:23 | INFO     | memory.history_manager:get_session_list:158 | 找到 2 个历史会话
2025-08-01 18:36:38 | INFO     | core.utils.config:load_config:36 | 配置文件加载成功
2025-08-01 18:36:38 | INFO     | memory.history_manager:__init__:44 | 历史记录目录: D:\Reverie\Reverie Agents RIO\history
2025-08-01 18:36:38 | INFO     | core.llm.model_manager:_load_config:56 | 配置文件加载成功
2025-08-01 18:36:38 | INFO     | core.llm.model_manager:_scan_models:72 | 发现模型: lucy_128k-Q8_0
2025-08-01 18:37:21 | INFO     | core.utils.config:load_config:36 | 配置文件加载成功
2025-08-01 18:37:21 | INFO     | memory.history_manager:__init__:44 | 历史记录目录: D:\Reverie\Reverie Agents RIO\history
2025-08-01 18:37:21 | INFO     | core.llm.model_manager:_load_config:56 | 配置文件加载成功
2025-08-01 18:37:21 | INFO     | core.llm.model_manager:_scan_models:72 | 发现模型: lucy_128k-Q8_0
2025-08-01 18:37:22 | INFO     | engine_2d.image_generator:__init__:118 | 2D Engine初始化完成，设备: cuda
2025-08-01 18:37:22 | INFO     | engine_2d.image_generator:__init__:119 | 发现 0 个可用模型
2025-08-01 18:37:22 | INFO     | engine_2d.image_generator:__init__:120 | 支持 14 种采样器
2025-08-01 18:37:22 | INFO     | personas.persona_manager:_load_personas:61 | 加载人设: 职场导师 (career_mentor)
2025-08-01 18:37:22 | INFO     | personas.persona_manager:_load_personas:61 | 加载人设: 推理专家 (reasoning_expert)
2025-08-01 18:37:22 | INFO     | personas.persona_manager:_load_personas:61 | 加载人设: 旅行向导 (travel_guide)
2025-08-01 18:37:22 | INFO     | personas.persona_manager:_load_personas:61 | 加载人设: 妻子模式 (wife)
2025-08-01 18:37:22 | INFO     | memory.history_manager:get_session_list:158 | 找到 2 个历史会话
2025-08-01 18:37:22 | INFO     | __main__:__init__:48 | Reverie Agents 桌面应用启动成功
2025-08-01 18:37:26 | INFO     | __main__:show_main_window:337 | 🎉 Reverie Agents 启动完成！
2025-08-01 18:37:35 | INFO     | personas.persona_manager:switch_persona:107 | 切换到人设: 妻子模式
2025-08-01 18:37:35 | INFO     | __main__:change_persona:191 | 切换人设: 妻子模式
2025-08-01 18:37:40 | INFO     | personas.persona_manager:_load_personas:61 | 加载人设: 职场导师 (career_mentor)
2025-08-01 18:37:40 | INFO     | personas.persona_manager:_load_personas:61 | 加载人设: 推理专家 (reasoning_expert)
2025-08-01 18:37:40 | INFO     | personas.persona_manager:_load_personas:61 | 加载人设: 旅行向导 (travel_guide)
2025-08-01 18:37:41 | INFO     | personas.persona_manager:_load_personas:61 | 加载人设: 妻子模式 (wife)
2025-08-01 18:37:41 | INFO     | personas.persona_manager:reload_personas:264 | 人设配置已重新加载
2025-08-01 18:37:41 | INFO     | personas.persona_manager:switch_persona:107 | 切换到人设: 职场导师
2025-08-01 18:37:41 | INFO     | __main__:change_persona:191 | 切换人设: 职场导师
2025-08-01 18:37:41 | INFO     | ui.app.components.persona_selector:refresh_personas:133 | 人设列表已刷新
2025-08-01 18:37:42 | INFO     | personas.persona_manager:_load_personas:61 | 加载人设: 职场导师 (career_mentor)
2025-08-01 18:37:42 | INFO     | personas.persona_manager:_load_personas:61 | 加载人设: 推理专家 (reasoning_expert)
2025-08-01 18:37:42 | INFO     | personas.persona_manager:_load_personas:61 | 加载人设: 旅行向导 (travel_guide)
2025-08-01 18:37:42 | INFO     | personas.persona_manager:_load_personas:61 | 加载人设: 妻子模式 (wife)
2025-08-01 18:37:42 | INFO     | personas.persona_manager:reload_personas:264 | 人设配置已重新加载
2025-08-01 18:37:42 | INFO     | ui.app.components.persona_selector:refresh_personas:133 | 人设列表已刷新
2025-08-01 18:37:43 | INFO     | personas.persona_manager:_load_personas:61 | 加载人设: 职场导师 (career_mentor)
2025-08-01 18:37:43 | INFO     | personas.persona_manager:_load_personas:61 | 加载人设: 推理专家 (reasoning_expert)
2025-08-01 18:37:43 | INFO     | personas.persona_manager:_load_personas:61 | 加载人设: 旅行向导 (travel_guide)
2025-08-01 18:37:43 | INFO     | personas.persona_manager:_load_personas:61 | 加载人设: 妻子模式 (wife)
2025-08-01 18:37:43 | INFO     | personas.persona_manager:reload_personas:264 | 人设配置已重新加载
2025-08-01 18:37:43 | INFO     | ui.app.components.persona_selector:refresh_personas:133 | 人设列表已刷新
2025-08-01 18:37:45 | INFO     | personas.persona_manager:switch_persona:107 | 切换到人设: 妻子模式
2025-08-01 18:37:45 | INFO     | __main__:change_persona:191 | 切换人设: 妻子模式
2025-08-01 18:37:47 | INFO     | core.utils.config:save_config:164 | 配置文件保存成功
2025-08-01 18:37:47 | INFO     | __main__:closeEvent:275 | 应用程序关闭
2025-08-01 18:42:52 | INFO     | core.utils.config:load_config:36 | 配置文件加载成功
2025-08-01 18:42:52 | INFO     | memory.history_manager:__init__:44 | 历史记录目录: D:\Reverie\Reverie Agents RIO\history
2025-08-01 18:42:52 | INFO     | core.llm.model_manager:_load_config:56 | 配置文件加载成功
2025-08-01 18:42:52 | INFO     | core.llm.model_manager:_scan_models:72 | 发现模型: lucy_128k-Q8_0
2025-08-01 18:42:55 | INFO     | engine_2d.image_generator:__init__:118 | 2D Engine初始化完成，设备: cuda
2025-08-01 18:42:55 | INFO     | engine_2d.image_generator:__init__:119 | 发现 0 个可用模型
2025-08-01 18:42:55 | INFO     | engine_2d.image_generator:__init__:120 | 支持 14 种采样器
2025-08-01 18:42:55 | INFO     | personas.persona_manager:_load_personas:61 | 加载人设: 职场导师 (career_mentor)
2025-08-01 18:42:55 | INFO     | personas.persona_manager:_load_personas:61 | 加载人设: 推理专家 (reasoning_expert)
2025-08-01 18:42:55 | INFO     | personas.persona_manager:_load_personas:61 | 加载人设: 旅行向导 (travel_guide)
2025-08-01 18:42:55 | INFO     | personas.persona_manager:_load_personas:61 | 加载人设: 妻子模式 (wife)
2025-08-01 18:42:55 | INFO     | memory.history_manager:get_session_list:158 | 找到 2 个历史会话
2025-08-01 18:42:55 | INFO     | __main__:__init__:48 | Reverie Agents 桌面应用启动成功
2025-08-01 18:42:59 | INFO     | __main__:show_main_window:337 | 🎉 Reverie Agents 启动完成！
2025-08-01 18:43:20 | INFO     | core.utils.config:save_config:164 | 配置文件保存成功
2025-08-01 18:43:22 | INFO     | core.utils.config:save_config:164 | 配置文件保存成功
2025-08-01 18:43:23 | INFO     | memory.history_manager:load_session:121 | 会话已加载: wife_20250801_162914.json (6 条消息)
2025-08-01 18:43:23 | INFO     | memory.context_engine:load_session:358 | 会话加载成功: wife_20250801_162914
2025-08-01 18:43:23 | INFO     | __main__:load_chat:179 | 加载对话: wife_20250801_162914
2025-08-01 18:43:24 | INFO     | memory.history_manager:load_session:121 | 会话已加载: career_mentor_20250801_162914.json (6 条消息)
2025-08-01 18:43:24 | INFO     | memory.context_engine:load_session:358 | 会话加载成功: career_mentor_20250801_162914
2025-08-01 18:43:24 | INFO     | __main__:load_chat:179 | 加载对话: career_mentor_20250801_162914
2025-08-01 18:43:24 | INFO     | memory.history_manager:load_session:121 | 会话已加载: wife_20250801_162914.json (6 条消息)
2025-08-01 18:43:24 | INFO     | memory.context_engine:load_session:358 | 会话加载成功: wife_20250801_162914
2025-08-01 18:43:24 | INFO     | __main__:load_chat:179 | 加载对话: wife_20250801_162914
2025-08-01 18:43:32 | INFO     | core.utils.config:save_config:164 | 配置文件保存成功
2025-08-01 18:43:32 | INFO     | __main__:closeEvent:275 | 应用程序关闭
2025-08-01 18:48:36 | INFO     | core.utils.config:load_config:36 | 配置文件加载成功
2025-08-01 18:48:36 | INFO     | memory.history_manager:__init__:44 | 历史记录目录: D:\Reverie\Reverie Agents RIO\history
2025-08-01 18:48:36 | INFO     | core.llm.model_manager:_load_config:56 | 配置文件加载成功
2025-08-01 18:48:36 | INFO     | core.llm.model_manager:_scan_models:72 | 发现模型: lucy_128k-Q8_0
2025-08-01 18:48:38 | INFO     | engine_2d.image_generator:__init__:118 | 2D Engine初始化完成，设备: cuda
2025-08-01 18:48:38 | INFO     | engine_2d.image_generator:__init__:119 | 发现 0 个可用模型
2025-08-01 18:48:38 | INFO     | engine_2d.image_generator:__init__:120 | 支持 14 种采样器
2025-08-01 18:48:38 | INFO     | personas.persona_manager:_load_personas:61 | 加载人设: 职场导师 (career_mentor)
2025-08-01 18:48:38 | INFO     | personas.persona_manager:_load_personas:61 | 加载人设: 推理专家 (reasoning_expert)
2025-08-01 18:48:38 | INFO     | personas.persona_manager:_load_personas:61 | 加载人设: 旅行向导 (travel_guide)
2025-08-01 18:48:38 | INFO     | personas.persona_manager:_load_personas:61 | 加载人设: 妻子模式 (wife)
2025-08-01 18:48:38 | INFO     | memory.history_manager:get_session_list:158 | 找到 2 个历史会话
2025-08-01 18:48:38 | INFO     | __main__:__init__:48 | Reverie Agents 桌面应用启动成功
2025-08-01 18:48:42 | INFO     | __main__:show_main_window:353 | 🎉 Reverie Agents 启动完成！
2025-08-01 18:59:42 | INFO     | personas.persona_manager:switch_persona:107 | 切换到人设: 妻子模式
2025-08-01 18:59:42 | INFO     | __main__:change_persona:207 | 切换人设: 妻子模式
2025-08-01 18:59:44 | INFO     | core.utils.config:save_config:164 | 配置文件保存成功
2025-08-01 18:59:44 | INFO     | __main__:closeEvent:291 | 应用程序关闭
2025-08-01 19:05:51 | INFO     | core.utils.config:load_config:36 | 配置文件加载成功
2025-08-01 19:05:51 | INFO     | memory.history_manager:__init__:44 | 历史记录目录: D:\Reverie\Reverie Agents RIO\history
2025-08-01 19:05:52 | INFO     | core.llm.model_manager:_load_config:56 | 配置文件加载成功
2025-08-01 19:05:52 | INFO     | core.llm.model_manager:_scan_models:72 | 发现模型: lucy_128k-Q8_0
2025-08-01 19:06:09 | INFO     | engine_2d.image_generator:__init__:118 | 2D Engine初始化完成，设备: cuda
2025-08-01 19:06:09 | INFO     | engine_2d.image_generator:__init__:119 | 发现 0 个可用模型
2025-08-01 19:06:09 | INFO     | engine_2d.image_generator:__init__:120 | 支持 14 种采样器
2025-08-01 19:06:09 | INFO     | personas.persona_manager:_load_personas:61 | 加载人设: 职场导师 (career_mentor)
2025-08-01 19:06:09 | INFO     | personas.persona_manager:_load_personas:61 | 加载人设: 推理专家 (reasoning_expert)
2025-08-01 19:06:09 | INFO     | personas.persona_manager:_load_personas:61 | 加载人设: 旅行向导 (travel_guide)
2025-08-01 19:06:09 | INFO     | personas.persona_manager:_load_personas:61 | 加载人设: 妻子模式 (wife)
2025-08-01 19:06:09 | INFO     | memory.history_manager:get_session_list:158 | 找到 2 个历史会话
2025-08-01 19:06:09 | INFO     | __main__:__init__:48 | Reverie Agents 桌面应用启动成功
2025-08-01 19:06:13 | INFO     | __main__:show_main_window:353 | 🎉 Reverie Agents 启动完成！
2025-08-01 19:06:28 | INFO     | memory.history_manager:load_session:121 | 会话已加载: wife_20250801_162914.json (6 条消息)
2025-08-01 19:06:28 | INFO     | memory.context_engine:load_session:358 | 会话加载成功: wife_20250801_162914
2025-08-01 19:06:28 | INFO     | __main__:load_chat:195 | 加载对话: wife_20250801_162914
2025-08-01 19:08:42 | INFO     | core.llm.model_manager:load_model:130 | 正在加载模型: lucy_128k-Q8_0
2025-08-01 19:08:42 | INFO     | core.llm.model_manager:load_model:131 | 模型路径: models/llm\lucy_128k-Q8_0.gguf
2025-08-01 19:08:42 | INFO     | core.llm.model_manager:load_model:132 | 上下文长度: 131072
2025-08-01 19:08:42 | INFO     | core.llm.model_manager:load_model:133 | GPU层数: -1
2025-08-01 19:08:42 | INFO     | core.llm.model_manager:load_model:134 | 线程数: 4
2025-08-01 19:08:42 | WARNING  | core.llm.model_manager:load_model:178 | CUDA加载失败，尝试CPU模式: exception: access violation reading 0x0000000000000000
2025-08-01 19:08:42 | ERROR    | core.llm.model_manager:load_model:188 | 模型加载失败: exception: access violation reading 0x0000000000000000
2025-08-01 19:08:42 | ERROR    | core.llm.model_manager:load_model:189 | 错误类型: OSError
2025-08-01 19:09:03 | INFO     | core.utils.config:save_config:164 | 配置文件保存成功
2025-08-01 19:09:03 | INFO     | __main__:closeEvent:291 | 应用程序关闭
2025-08-01 19:28:40 | INFO     | core.utils.config:load_config:36 | 配置文件加载成功
2025-08-01 19:28:40 | INFO     | memory.history_manager:__init__:44 | 历史记录目录: D:\Reverie\Reverie Agents RIO\history
2025-08-01 19:28:40 | INFO     | core.llm.model_manager:_load_config:56 | 配置文件加载成功
2025-08-01 19:28:40 | INFO     | core.llm.model_manager:_scan_models:72 | 发现模型: lucy_128k-Q8_0
2025-08-01 19:28:44 | INFO     | engine_2d.image_generator:__init__:118 | 2D Engine初始化完成，设备: cuda
2025-08-01 19:28:44 | INFO     | engine_2d.image_generator:__init__:119 | 发现 0 个可用模型
2025-08-01 19:28:44 | INFO     | engine_2d.image_generator:__init__:120 | 支持 14 种采样器
2025-08-01 19:28:44 | INFO     | personas.persona_manager:_load_personas:61 | 加载人设: 职场导师 (career_mentor)
2025-08-01 19:28:44 | INFO     | personas.persona_manager:_load_personas:61 | 加载人设: 推理专家 (reasoning_expert)
2025-08-01 19:28:44 | INFO     | personas.persona_manager:_load_personas:61 | 加载人设: 旅行向导 (travel_guide)
2025-08-01 19:28:44 | INFO     | personas.persona_manager:_load_personas:61 | 加载人设: 妻子模式 (wife)
2025-08-01 19:28:44 | INFO     | memory.history_manager:get_session_list:158 | 找到 2 个历史会话
2025-08-01 19:28:44 | INFO     | ui.app.components.agents_hub:load_agents:551 | 已加载 4 个Agent
2025-08-01 19:28:44 | INFO     | __main__:__init__:49 | Reverie Agents 桌面应用启动成功
2025-08-01 19:28:48 | INFO     | __main__:show_main_window:397 | 🎉 Reverie Agents 启动完成！
2025-08-01 19:29:12 | INFO     | ui.app.components.persona_selector:set_current_persona:294 | 设置当前人设: wife
2025-08-01 19:29:12 | INFO     | personas.persona_manager:switch_persona:107 | 切换到人设: 妻子模式
2025-08-01 19:29:12 | INFO     | __main__:change_persona:251 | 切换人设: 妻子模式
2025-08-01 19:29:12 | INFO     | __main__:select_agent_from_hub:155 | 从Hub选择了Agent: wife
2025-08-01 19:29:16 | INFO     | personas.persona_manager:switch_persona:107 | 切换到人设: 妻子模式
2025-08-01 19:29:16 | INFO     | __main__:change_persona:251 | 切换人设: 妻子模式
2025-08-01 19:29:21 | INFO     | ui.app.components.persona_selector:set_current_persona:294 | 设置当前人设: wife
2025-08-01 19:29:21 | INFO     | personas.persona_manager:switch_persona:107 | 切换到人设: 妻子模式
2025-08-01 19:29:21 | INFO     | __main__:change_persona:251 | 切换人设: 妻子模式
2025-08-01 19:29:21 | INFO     | __main__:select_agent_from_hub:155 | 从Hub选择了Agent: wife
2025-08-01 19:29:33 | INFO     | core.llm.model_manager:load_model:130 | 正在加载模型: lucy_128k-Q8_0
2025-08-01 19:29:33 | INFO     | core.llm.model_manager:load_model:131 | 模型路径: models/llm\lucy_128k-Q8_0.gguf
2025-08-01 19:29:33 | INFO     | core.llm.model_manager:load_model:132 | 上下文长度: 131072
2025-08-01 19:29:33 | INFO     | core.llm.model_manager:load_model:133 | GPU层数: -1
2025-08-01 19:29:33 | INFO     | core.llm.model_manager:load_model:134 | 线程数: 4
2025-08-01 19:29:33 | WARNING  | core.llm.model_manager:load_model:178 | CUDA加载失败，尝试CPU模式: exception: access violation reading 0x0000000000000000
2025-08-01 19:29:33 | ERROR    | core.llm.model_manager:load_model:188 | 模型加载失败: exception: access violation reading 0x0000000000000000
2025-08-01 19:29:33 | ERROR    | core.llm.model_manager:load_model:189 | 错误类型: OSError
2025-08-01 19:29:49 | INFO     | personas.persona_manager:_load_personas:61 | 加载人设: 职场导师 (career_mentor)
2025-08-01 19:29:49 | INFO     | personas.persona_manager:_load_personas:61 | 加载人设: 推理专家 (reasoning_expert)
2025-08-01 19:29:49 | INFO     | personas.persona_manager:_load_personas:61 | 加载人设: 旅行向导 (travel_guide)
2025-08-01 19:29:49 | INFO     | personas.persona_manager:_load_personas:61 | 加载人设: 妻子模式 (wife)
2025-08-01 19:29:49 | INFO     | personas.persona_manager:reload_personas:264 | 人设配置已重新加载
2025-08-01 19:29:49 | INFO     | personas.persona_manager:switch_persona:107 | 切换到人设: 职场导师
2025-08-01 19:29:49 | INFO     | __main__:change_persona:251 | 切换人设: 职场导师
2025-08-01 19:29:49 | INFO     | ui.app.components.persona_selector:set_current_persona:294 | 设置当前人设: career_mentor
2025-08-01 19:29:49 | INFO     | ui.app.components.persona_selector:refresh_personas:274 | 人设列表已刷新
2025-08-01 19:29:49 | INFO     | personas.persona_manager:_load_personas:61 | 加载人设: 职场导师 (career_mentor)
2025-08-01 19:29:49 | INFO     | personas.persona_manager:_load_personas:61 | 加载人设: 推理专家 (reasoning_expert)
2025-08-01 19:29:49 | INFO     | personas.persona_manager:_load_personas:61 | 加载人设: 旅行向导 (travel_guide)
2025-08-01 19:29:49 | INFO     | personas.persona_manager:_load_personas:61 | 加载人设: 妻子模式 (wife)
2025-08-01 19:29:49 | INFO     | personas.persona_manager:reload_personas:264 | 人设配置已重新加载
2025-08-01 19:29:49 | INFO     | ui.app.components.persona_selector:set_current_persona:294 | 设置当前人设: career_mentor
2025-08-01 19:29:49 | INFO     | ui.app.components.persona_selector:refresh_personas:274 | 人设列表已刷新
2025-08-01 19:29:50 | INFO     | personas.persona_manager:_load_personas:61 | 加载人设: 职场导师 (career_mentor)
2025-08-01 19:29:50 | INFO     | personas.persona_manager:_load_personas:61 | 加载人设: 推理专家 (reasoning_expert)
2025-08-01 19:29:50 | INFO     | personas.persona_manager:_load_personas:61 | 加载人设: 旅行向导 (travel_guide)
2025-08-01 19:29:50 | INFO     | personas.persona_manager:_load_personas:61 | 加载人设: 妻子模式 (wife)
2025-08-01 19:29:50 | INFO     | personas.persona_manager:reload_personas:264 | 人设配置已重新加载
2025-08-01 19:29:50 | INFO     | ui.app.components.persona_selector:set_current_persona:294 | 设置当前人设: career_mentor
2025-08-01 19:29:50 | INFO     | ui.app.components.persona_selector:refresh_personas:274 | 人设列表已刷新
2025-08-01 19:29:50 | INFO     | personas.persona_manager:_load_personas:61 | 加载人设: 职场导师 (career_mentor)
2025-08-01 19:29:50 | INFO     | personas.persona_manager:_load_personas:61 | 加载人设: 推理专家 (reasoning_expert)
2025-08-01 19:29:50 | INFO     | personas.persona_manager:_load_personas:61 | 加载人设: 旅行向导 (travel_guide)
2025-08-01 19:29:50 | INFO     | personas.persona_manager:_load_personas:61 | 加载人设: 妻子模式 (wife)
2025-08-01 19:29:50 | INFO     | personas.persona_manager:reload_personas:264 | 人设配置已重新加载
2025-08-01 19:29:50 | INFO     | ui.app.components.persona_selector:set_current_persona:294 | 设置当前人设: career_mentor
2025-08-01 19:29:50 | INFO     | ui.app.components.persona_selector:refresh_personas:274 | 人设列表已刷新
2025-08-01 19:29:51 | INFO     | personas.persona_manager:switch_persona:107 | 切换到人设: 妻子模式
2025-08-01 19:29:51 | INFO     | __main__:change_persona:251 | 切换人设: 妻子模式
2025-08-01 19:29:55 | INFO     | ui.app.components.persona_selector:set_current_persona:294 | 设置当前人设: wife
2025-08-01 19:29:55 | INFO     | personas.persona_manager:switch_persona:107 | 切换到人设: 妻子模式
2025-08-01 19:29:55 | INFO     | __main__:change_persona:251 | 切换人设: 妻子模式
2025-08-01 19:29:55 | INFO     | __main__:select_agent_from_hub:155 | 从Hub选择了Agent: wife
2025-08-01 19:29:59 | INFO     | personas.persona_manager:switch_persona:107 | 切换到人设: 职场导师
2025-08-01 19:29:59 | INFO     | __main__:change_persona:251 | 切换人设: 职场导师
2025-08-01 19:29:59 | INFO     | ui.app.components.persona_selector:set_current_persona:294 | 设置当前人设: career_mentor
2025-08-01 19:29:59 | INFO     | personas.persona_manager:switch_persona:107 | 切换到人设: 职场导师
2025-08-01 19:29:59 | INFO     | __main__:change_persona:251 | 切换人设: 职场导师
2025-08-01 19:29:59 | INFO     | __main__:select_agent_from_hub:155 | 从Hub选择了Agent: career_mentor
2025-08-01 19:30:01 | INFO     | ui.app.components.persona_selector:set_current_persona:294 | 设置当前人设: wife
2025-08-01 19:30:01 | INFO     | personas.persona_manager:switch_persona:107 | 切换到人设: 妻子模式
2025-08-01 19:30:01 | INFO     | __main__:change_persona:251 | 切换人设: 妻子模式
2025-08-01 19:30:01 | INFO     | __main__:select_agent_from_hub:155 | 从Hub选择了Agent: wife
2025-08-01 19:30:04 | INFO     | ui.app.components.persona_selector:set_current_persona:294 | 设置当前人设: wife
2025-08-01 19:30:04 | INFO     | personas.persona_manager:switch_persona:107 | 切换到人设: 妻子模式
2025-08-01 19:30:04 | INFO     | __main__:change_persona:251 | 切换人设: 妻子模式
2025-08-01 19:30:04 | INFO     | __main__:select_agent_from_hub:155 | 从Hub选择了Agent: wife
2025-08-01 19:30:08 | INFO     | personas.persona_manager:switch_persona:107 | 切换到人设: 推理专家
2025-08-01 19:30:08 | INFO     | __main__:change_persona:251 | 切换人设: 推理专家
2025-08-01 19:30:08 | INFO     | ui.app.components.persona_selector:set_current_persona:294 | 设置当前人设: reasoning_expert
2025-08-01 19:30:08 | INFO     | personas.persona_manager:switch_persona:107 | 切换到人设: 推理专家
2025-08-01 19:30:08 | INFO     | __main__:change_persona:251 | 切换人设: 推理专家
2025-08-01 19:30:08 | INFO     | __main__:select_agent_from_hub:155 | 从Hub选择了Agent: reasoning_expert
2025-08-01 19:30:10 | INFO     | personas.persona_manager:switch_persona:107 | 切换到人设: 旅行向导
2025-08-01 19:30:10 | INFO     | __main__:change_persona:251 | 切换人设: 旅行向导
2025-08-01 19:30:10 | INFO     | ui.app.components.persona_selector:set_current_persona:294 | 设置当前人设: travel_guide
2025-08-01 19:30:10 | INFO     | personas.persona_manager:switch_persona:107 | 切换到人设: 旅行向导
2025-08-01 19:30:10 | INFO     | __main__:change_persona:251 | 切换人设: 旅行向导
2025-08-01 19:30:10 | INFO     | __main__:select_agent_from_hub:155 | 从Hub选择了Agent: travel_guide
2025-08-01 19:30:12 | INFO     | personas.persona_manager:switch_persona:107 | 切换到人设: 妻子模式
2025-08-01 19:30:12 | INFO     | __main__:change_persona:251 | 切换人设: 妻子模式
2025-08-01 19:35:41 | INFO     | core.utils.config:save_config:164 | 配置文件保存成功
2025-08-01 19:35:41 | INFO     | __main__:closeEvent:335 | 应用程序关闭
2025-08-01 20:49:45 | INFO     | core.utils.config:load_config:36 | 配置文件加载成功
2025-08-01 20:49:45 | INFO     | memory.history_manager:__init__:44 | 历史记录目录: D:\Reverie\Reverie Agents RIO\history
2025-08-01 20:49:45 | INFO     | core.llm.model_manager:_load_config:56 | 配置文件加载成功
2025-08-01 20:49:45 | INFO     | core.llm.model_manager:_scan_models:72 | 发现模型: lucy_128k-Q8_0
2025-08-01 21:17:38 | INFO     | core.utils.config:load_config:36 | 配置文件加载成功
2025-08-01 21:17:38 | INFO     | memory.history_manager:__init__:44 | 历史记录目录: D:\Reverie\Reverie Agents RIO\history
2025-08-01 21:17:38 | INFO     | core.llm.model_manager:_load_config:56 | 配置文件加载成功
2025-08-01 21:17:38 | INFO     | core.llm.model_manager:_scan_models:72 | 发现模型: lucy_128k-Q8_0
2025-08-01 21:49:28 | INFO     | core.utils.config:load_config:36 | 配置文件加载成功
2025-08-01 21:49:28 | INFO     | memory.history_manager:__init__:44 | 历史记录目录: D:\Reverie\Reverie Agents RIO\history
2025-08-01 21:49:28 | INFO     | core.llm.model_manager:_load_config:56 | 配置文件加载成功
2025-08-01 21:49:28 | INFO     | core.llm.model_manager:_scan_models:72 | 发现模型: lucy_128k-Q8_0
2025-08-01 22:04:49 | INFO     | core.utils.config:load_config:36 | 配置文件加载成功
2025-08-01 22:04:49 | INFO     | memory.history_manager:__init__:44 | 历史记录目录: D:\Reverie\Reverie Agents RIO\history
2025-08-01 22:04:49 | INFO     | core.llm.model_manager:_load_config:56 | 配置文件加载成功
2025-08-01 22:04:49 | INFO     | core.llm.model_manager:_scan_models:72 | 发现模型: lucy_128k-Q8_0
2025-08-01 22:04:56 | INFO     | engine_2d.image_generator:__init__:118 | 2D Engine初始化完成，设备: cuda
2025-08-01 22:04:56 | INFO     | engine_2d.image_generator:__init__:119 | 发现 0 个可用模型
2025-08-01 22:04:56 | INFO     | engine_2d.image_generator:__init__:120 | 支持 14 种采样器
2025-08-01 22:04:56 | INFO     | personas.persona_manager:_load_personas:61 | 加载人设: 职场导师 (career_mentor)
2025-08-01 22:04:56 | INFO     | personas.persona_manager:_load_personas:61 | 加载人设: 推理专家 (reasoning_expert)
2025-08-01 22:04:56 | INFO     | personas.persona_manager:_load_personas:61 | 加载人设: 旅行向导 (travel_guide)
2025-08-01 22:04:56 | INFO     | personas.persona_manager:_load_personas:61 | 加载人设: 妻子模式 (wife)
2025-08-01 22:04:56 | INFO     | memory.history_manager:get_session_list:158 | 找到 2 个历史会话
2025-08-01 22:04:56 | INFO     | ui.app.components.agents_hub:load_agents:551 | 已加载 4 个Agent
2025-08-01 22:04:56 | INFO     | __main__:__init__:49 | Reverie Agents 桌面应用启动成功
2025-08-02 16:56:11 | INFO     | core.utils.config:load_config:36 | 配置文件加载成功
2025-08-02 16:56:11 | INFO     | memory.history_manager:__init__:44 | 历史记录目录: D:\Reverie\Reverie Agents RIO\history
2025-08-02 16:56:13 | INFO     | core.llm.model_manager:_load_config:56 | 配置文件加载成功
2025-08-02 16:56:13 | INFO     | core.llm.model_manager:_scan_models:72 | 发现模型: lucy_128k-Q8_0
2025-08-02 16:57:15 | INFO     | engine_2d.image_generator:__init__:118 | 2D Engine初始化完成，设备: cuda
2025-08-02 16:57:15 | INFO     | engine_2d.image_generator:__init__:119 | 发现 0 个可用模型
2025-08-02 16:57:15 | INFO     | engine_2d.image_generator:__init__:120 | 支持 14 种采样器
2025-08-02 16:57:15 | INFO     | personas.persona_manager:_load_personas:61 | 加载人设: 职场导师 (career_mentor)
2025-08-02 16:57:15 | INFO     | personas.persona_manager:_load_personas:61 | 加载人设: 推理专家 (reasoning_expert)
2025-08-02 16:57:15 | INFO     | personas.persona_manager:_load_personas:61 | 加载人设: 旅行向导 (travel_guide)
2025-08-02 16:57:15 | INFO     | personas.persona_manager:_load_personas:61 | 加载人设: 妻子模式 (wife)
2025-08-02 16:57:15 | INFO     | memory.history_manager:get_session_list:158 | 找到 2 个历史会话
2025-08-02 16:57:15 | INFO     | ui.app.components.agents_hub:load_agents:551 | 已加载 4 个Agent
2025-08-02 16:57:16 | INFO     | __main__:__init__:49 | Reverie Agents 桌面应用启动成功
2025-08-02 16:57:19 | INFO     | __main__:show_main_window:397 | 🎉 Reverie Agents 启动完成！
2025-08-02 16:58:49 | INFO     | memory.history_manager:load_session:121 | 会话已加载: wife_20250801_162914.json (6 条消息)
2025-08-02 16:58:49 | INFO     | memory.context_engine:load_session:358 | 会话加载成功: wife_20250801_162914
2025-08-02 16:58:49 | INFO     | __main__:load_chat:239 | 加载对话: wife_20250801_162914
2025-08-02 16:58:57 | INFO     | memory.history_manager:load_session:121 | 会话已加载: career_mentor_20250801_162914.json (6 条消息)
2025-08-02 16:58:57 | INFO     | memory.context_engine:load_session:358 | 会话加载成功: career_mentor_20250801_162914
2025-08-02 16:58:57 | INFO     | __main__:load_chat:239 | 加载对话: career_mentor_20250801_162914
2025-08-02 16:59:00 | INFO     | memory.history_manager:load_session:121 | 会话已加载: wife_20250801_162914.json (6 条消息)
2025-08-02 16:59:00 | INFO     | memory.context_engine:load_session:358 | 会话加载成功: wife_20250801_162914
2025-08-02 16:59:00 | INFO     | __main__:load_chat:239 | 加载对话: wife_20250801_162914
2025-08-02 17:01:33 | INFO     | core.utils.config:load_config:36 | 配置文件加载成功
2025-08-02 17:01:33 | INFO     | memory.history_manager:__init__:44 | 历史记录目录: D:\Reverie\Reverie Agents RIO\history
2025-08-02 17:01:33 | INFO     | core.llm.model_manager:_load_config:56 | 配置文件加载成功
2025-08-02 17:01:33 | INFO     | core.llm.model_manager:_scan_models:72 | 发现模型: lucy_128k-Q8_0
2025-08-02 17:01:37 | INFO     | engine_2d.image_generator:__init__:118 | 2D Engine初始化完成，设备: cuda
2025-08-02 17:01:37 | INFO     | engine_2d.image_generator:__init__:119 | 发现 0 个可用模型
2025-08-02 17:01:37 | INFO     | engine_2d.image_generator:__init__:120 | 支持 14 种采样器
2025-08-02 17:01:37 | INFO     | personas.persona_manager:_load_personas:61 | 加载人设: 职场导师 (career_mentor)
2025-08-02 17:01:37 | INFO     | personas.persona_manager:_load_personas:61 | 加载人设: 推理专家 (reasoning_expert)
2025-08-02 17:01:37 | INFO     | personas.persona_manager:_load_personas:61 | 加载人设: 旅行向导 (travel_guide)
2025-08-02 17:01:37 | INFO     | personas.persona_manager:_load_personas:61 | 加载人设: 妻子模式 (wife)
2025-08-02 17:01:37 | INFO     | memory.history_manager:get_session_list:158 | 找到 2 个历史会话
2025-08-02 17:01:37 | INFO     | ui.app.components.agents_hub:load_agents:551 | 已加载 4 个Agent
2025-08-02 17:01:37 | INFO     | __main__:__init__:49 | Reverie Agents 桌面应用启动成功
2025-08-02 17:01:41 | INFO     | __main__:show_main_window:397 | 🎉 Reverie Agents 启动完成！
2025-08-02 17:04:31 | INFO     | memory.history_manager:load_session:121 | 会话已加载: wife_20250801_162914.json (6 条消息)
2025-08-02 17:04:31 | INFO     | memory.context_engine:load_session:358 | 会话加载成功: wife_20250801_162914
2025-08-02 17:04:31 | INFO     | __main__:load_chat:239 | 加载对话: wife_20250801_162914
2025-08-02 17:11:46 | INFO     | core.utils.config:load_config:36 | 配置文件加载成功
2025-08-02 17:11:46 | INFO     | memory.history_manager:__init__:44 | 历史记录目录: D:\Reverie\Reverie Agents RIO\history
2025-08-02 17:11:46 | INFO     | core.llm.model_manager:_load_config:56 | 配置文件加载成功
2025-08-02 17:11:46 | INFO     | core.llm.model_manager:_scan_models:72 | 发现模型: lucy_128k-Q8_0
2025-08-02 17:11:50 | INFO     | engine_2d.image_generator:__init__:118 | 2D Engine初始化完成，设备: cuda
2025-08-02 17:11:50 | INFO     | engine_2d.image_generator:__init__:119 | 发现 0 个可用模型
2025-08-02 17:11:50 | INFO     | engine_2d.image_generator:__init__:120 | 支持 14 种采样器
2025-08-02 17:11:50 | INFO     | personas.persona_manager:_load_personas:61 | 加载人设: 职场导师 (career_mentor)
2025-08-02 17:11:50 | INFO     | personas.persona_manager:_load_personas:61 | 加载人设: 推理专家 (reasoning_expert)
2025-08-02 17:11:50 | INFO     | personas.persona_manager:_load_personas:61 | 加载人设: 旅行向导 (travel_guide)
2025-08-02 17:11:50 | INFO     | personas.persona_manager:_load_personas:61 | 加载人设: 妻子模式 (wife)
2025-08-02 17:11:50 | INFO     | memory.history_manager:get_session_list:158 | 找到 2 个历史会话
2025-08-02 17:11:50 | INFO     | ui.app.components.agents_hub:load_agents:551 | 已加载 4 个Agent
2025-08-02 17:11:50 | INFO     | __main__:__init__:49 | Reverie Agents 桌面应用启动成功
2025-08-02 17:11:54 | INFO     | __main__:show_main_window:397 | 🎉 Reverie Agents 启动完成！
2025-08-02 17:11:55 | INFO     | memory.history_manager:load_session:121 | 会话已加载: wife_20250801_162914.json (6 条消息)
2025-08-02 17:11:55 | INFO     | memory.context_engine:load_session:358 | 会话加载成功: wife_20250801_162914
2025-08-02 17:11:55 | INFO     | __main__:load_chat:239 | 加载对话: wife_20250801_162914
2025-08-02 17:36:18 | INFO     | core.utils.config:load_config:36 | 配置文件加载成功
2025-08-02 17:36:18 | INFO     | memory.history_manager:__init__:44 | 历史记录目录: D:\Reverie\Reverie Agents RIO\history
2025-08-02 17:36:18 | INFO     | core.llm.model_manager:_load_config:56 | 配置文件加载成功
2025-08-02 17:36:18 | INFO     | core.llm.model_manager:_scan_models:72 | 发现模型: lucy_128k-Q8_0
2025-08-02 17:36:22 | INFO     | engine_2d.image_generator:__init__:118 | 2D Engine初始化完成，设备: cuda
2025-08-02 17:36:22 | INFO     | engine_2d.image_generator:__init__:119 | 发现 0 个可用模型
2025-08-02 17:36:22 | INFO     | engine_2d.image_generator:__init__:120 | 支持 14 种采样器
2025-08-02 17:36:22 | INFO     | personas.persona_manager:_load_personas:61 | 加载人设: 职场导师 (career_mentor)
2025-08-02 17:36:22 | INFO     | personas.persona_manager:_load_personas:61 | 加载人设: 推理专家 (reasoning_expert)
2025-08-02 17:36:22 | INFO     | personas.persona_manager:_load_personas:61 | 加载人设: 旅行向导 (travel_guide)
2025-08-02 17:36:22 | INFO     | personas.persona_manager:_load_personas:61 | 加载人设: 妻子模式 (wife)
2025-08-02 17:36:22 | INFO     | memory.history_manager:get_session_list:158 | 找到 2 个历史会话
2025-08-02 17:36:22 | INFO     | ui.app.components.agents_hub:load_agents:551 | 已加载 4 个Agent
2025-08-02 17:36:22 | INFO     | __main__:__init__:49 | Reverie Agents 桌面应用启动成功
2025-08-02 17:36:26 | INFO     | __main__:show_main_window:397 | 🎉 Reverie Agents 启动完成！
2025-08-02 17:36:28 | INFO     | memory.history_manager:load_session:121 | 会话已加载: wife_20250801_162914.json (6 条消息)
2025-08-02 17:36:28 | INFO     | memory.context_engine:load_session:358 | 会话加载成功: wife_20250801_162914
2025-08-02 17:36:28 | INFO     | __main__:load_chat:239 | 加载对话: wife_20250801_162914
2025-08-02 17:36:32 | INFO     | core.utils.config:save_config:164 | 配置文件保存成功
2025-08-02 17:36:32 | INFO     | __main__:closeEvent:335 | 应用程序关闭
2025-08-02 17:54:17 | INFO     | core.utils.config:load_config:36 | 配置文件加载成功
2025-08-02 17:54:17 | INFO     | memory.history_manager:__init__:44 | 历史记录目录: D:\Reverie\Reverie Agents RIO\history
2025-08-02 17:54:17 | INFO     | core.llm.model_manager:_load_config:56 | 配置文件加载成功
2025-08-02 17:54:17 | INFO     | core.llm.model_manager:_scan_models:72 | 发现模型: lucy_128k-Q8_0
2025-08-02 17:54:20 | INFO     | engine_2d.image_generator:__init__:118 | 2D Engine初始化完成，设备: cuda
2025-08-02 17:54:20 | INFO     | engine_2d.image_generator:__init__:119 | 发现 0 个可用模型
2025-08-02 17:54:20 | INFO     | engine_2d.image_generator:__init__:120 | 支持 14 种采样器
2025-08-02 17:54:20 | INFO     | personas.persona_manager:_load_personas:61 | 加载人设: 职场导师 (career_mentor)
2025-08-02 17:54:20 | INFO     | personas.persona_manager:_load_personas:61 | 加载人设: 推理专家 (reasoning_expert)
2025-08-02 17:54:20 | INFO     | personas.persona_manager:_load_personas:61 | 加载人设: 旅行向导 (travel_guide)
2025-08-02 17:54:20 | INFO     | personas.persona_manager:_load_personas:61 | 加载人设: 妻子模式 (wife)
2025-08-02 17:54:20 | INFO     | memory.history_manager:get_session_list:158 | 找到 2 个历史会话
2025-08-02 17:54:20 | INFO     | ui.app.components.agents_hub:load_agents:551 | 已加载 4 个Agent
2025-08-02 17:54:20 | INFO     | __main__:__init__:49 | Reverie Agents 桌面应用启动成功
2025-08-02 17:54:24 | INFO     | __main__:show_main_window:397 | 🎉 Reverie Agents 启动完成！
2025-08-02 17:54:28 | INFO     | memory.history_manager:load_session:121 | 会话已加载: wife_20250801_162914.json (6 条消息)
2025-08-02 17:54:28 | INFO     | memory.context_engine:load_session:358 | 会话加载成功: wife_20250801_162914
2025-08-02 17:54:28 | INFO     | __main__:load_chat:239 | 加载对话: wife_20250801_162914
2025-08-02 17:54:29 | INFO     | memory.history_manager:load_session:121 | 会话已加载: career_mentor_20250801_162914.json (6 条消息)
2025-08-02 17:54:29 | INFO     | memory.context_engine:load_session:358 | 会话加载成功: career_mentor_20250801_162914
2025-08-02 17:54:29 | INFO     | __main__:load_chat:239 | 加载对话: career_mentor_20250801_162914
2025-08-02 17:54:29 | INFO     | memory.history_manager:load_session:121 | 会话已加载: wife_20250801_162914.json (6 条消息)
2025-08-02 17:54:29 | INFO     | memory.context_engine:load_session:358 | 会话加载成功: wife_20250801_162914
2025-08-02 17:54:29 | INFO     | __main__:load_chat:239 | 加载对话: wife_20250801_162914
2025-08-02 17:54:30 | INFO     | memory.history_manager:load_session:121 | 会话已加载: career_mentor_20250801_162914.json (6 条消息)
2025-08-02 17:54:30 | INFO     | memory.context_engine:load_session:358 | 会话加载成功: career_mentor_20250801_162914
2025-08-02 17:54:30 | INFO     | __main__:load_chat:239 | 加载对话: career_mentor_20250801_162914
2025-08-02 17:54:30 | INFO     | memory.history_manager:load_session:121 | 会话已加载: wife_20250801_162914.json (6 条消息)
2025-08-02 17:54:30 | INFO     | memory.context_engine:load_session:358 | 会话加载成功: wife_20250801_162914
2025-08-02 17:54:30 | INFO     | __main__:load_chat:239 | 加载对话: wife_20250801_162914
2025-08-02 17:54:30 | INFO     | memory.history_manager:load_session:121 | 会话已加载: career_mentor_20250801_162914.json (6 条消息)
2025-08-02 17:54:30 | INFO     | memory.context_engine:load_session:358 | 会话加载成功: career_mentor_20250801_162914
2025-08-02 17:54:30 | INFO     | __main__:load_chat:239 | 加载对话: career_mentor_20250801_162914
2025-08-02 17:54:31 | INFO     | memory.history_manager:load_session:121 | 会话已加载: wife_20250801_162914.json (6 条消息)
2025-08-02 17:54:31 | INFO     | memory.context_engine:load_session:358 | 会话加载成功: wife_20250801_162914
2025-08-02 17:54:31 | INFO     | __main__:load_chat:239 | 加载对话: wife_20250801_162914
2025-08-02 17:54:31 | INFO     | memory.history_manager:load_session:121 | 会话已加载: career_mentor_20250801_162914.json (6 条消息)
2025-08-02 17:54:31 | INFO     | memory.context_engine:load_session:358 | 会话加载成功: career_mentor_20250801_162914
2025-08-02 17:54:31 | INFO     | __main__:load_chat:239 | 加载对话: career_mentor_20250801_162914
2025-08-02 17:54:46 | INFO     | core.utils.config:save_config:164 | 配置文件保存成功
2025-08-02 17:54:46 | INFO     | __main__:closeEvent:335 | 应用程序关闭
